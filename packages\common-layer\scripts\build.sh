#!/usr/bin/env bash
#+--------------------------------------------------------------------------------+
#|                                                                                |
#|          <AUTHOR> <<EMAIL>>         |
#|                                                                                |
#+--------------------------------------------------------------------------------+

# https://explainshell.com/explain?cmd=set%20-euxo%20pipefail

set -euxo pipefail

echo "Copying Node Modules"

cp -r node_modules nodejs
cp package.json nodejs

echo "Finalizing"

exit 0
