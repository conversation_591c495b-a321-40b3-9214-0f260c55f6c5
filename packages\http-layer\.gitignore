*.js
!jest.config.js
*.d.ts
node_modules

# TypeScript build info
tsconfig.tsbuildinfo

# CDK asset staging directory
.cdk.staging
cdk.out

# Parcel default cache directory
.parcel-cache

dist/
.dist/
build/

# testing
coverage

# misc
.DS_Store

npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

.env
.env.dev
.env.prod

# AWS SAM
.aws-sam
template.yaml

# Codeship
tmp
codeship.aes

# AWS
aws_credentials.env


# Deployment Variables
dev_environment.env
prod_environment.env

#CDK
cloudformation
cdk-exports.json

# Lambdas Deployment
lambdas.deployment.json