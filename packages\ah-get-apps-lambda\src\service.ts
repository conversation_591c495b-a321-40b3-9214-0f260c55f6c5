import { AppResponse, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';

const appsRepository = new AppsRepository();

export async function getAllApps(): Promise<AppResponse[]> {
    // Solo devolver aplicativos activos (no eliminados)
    const apps = await appsRepository.findWithBasicFilters({ deleted: false });
    return apps.map(createAppResponse);
}