import { AppResponse, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';

const appsRepository = new AppsRepository();

export async function getAllApps(): Promise<AppResponse[]> {
    // Solo devolver aplicativos activos (no eliminados) - UPDATED 2025-06-28
    const apps = await appsRepository.findWithBasicFilters({ deleted: false });
    console.log('Filtering deleted apps - only returning active apps');
    return apps.map(createAppResponse);
}