/**
 * CORS Headers for Access-Control-Allow-Headers.
 *
 * These headers are commonly used in CORS requests and define which headers
 * can be used when making the actual request.
 *
 * @public
 */
export enum CORSHeaders {
  /**
   * Standard CORS header for allowing headers
   */
  ACCESS_CONTROL_ALLOW_HEADERS = 'Access-Control-Allow-Headers',

  /**
   * Origin header for CORS requests
   */
  ORIGIN = 'Origin',

  /**
   * Accept header for content negotiation
   */
  ACCEPT = 'Accept',

  /**
   * X-Requested-With header for AJAX requests
   */
  X_REQUESTED_WITH = 'X-Requested-With',

  /**
   * Content-Type header for request body type
   */
  CONTENT_TYPE = 'Content-Type',

  /**
   * Access-Control-Request-Method header for preflight requests
   */
  ACCESS_CONTROL_REQUEST_METHOD = 'Access-Control-Request-Method',

  /**
   * Access-Control-Request-Headers header for preflight requests
   */
  ACCESS_CONTROL_REQUEST_HEADERS = 'Access-Control-Request-Headers',

  /**
   * Authorization header for authentication
   */
  AUTHORIZATION = 'Authorization',

  /**
   * Custom username header
   */
  X_USERNAME = 'x-username',
}

/**
 * @public
 */
export default CORSHeaders 