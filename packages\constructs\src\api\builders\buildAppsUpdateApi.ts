import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsUpdateApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
    idParamName?: string;
};

export const buildAppsUpdateApi = (
    scope: Construct,
    props: AppsUpdateApiProps,
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod',
    } = props;

    const updateFunction = new lambda.Function(scope, 'AppsUpdateFunction', {
        functionName: 'ah-update-apps-lambda',
        description: `PATCH API handler for apps update in ${stage} environment`,
        code,
        handler: 'index.updateApp',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('PATCH', new api.LambdaIntegration(updateFunction), {
        authorizationType: api.AuthorizationType.NONE,
        operationName: 'updateApp',
    });
};
