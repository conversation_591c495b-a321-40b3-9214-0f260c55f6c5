{"name": "@microsip/util", "version": "0.0.1", "private": true, "description": "Microsip Arquitectura Hibrida Apps Microservice Utils", "engines": {"node": ">=16.20.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Microsip", "Arquitectura Hibrida"], "license": "ISC", "homepage": ".", "main": ".dist/index.js", "scripts": {"prebuild": "rimraf .dist", "build": "tsc", "test": "jest", "test:ci": "jest -u --detect<PERSON><PERSON><PERSON><PERSON>les --forceExit", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {}, "dependencies": {"debug": "^4.3.1", "dotenv": "^8.2.0", "find-up": "^5.0.0", "rimraf": "^3.0.2", "semver": "^7.3.8"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}