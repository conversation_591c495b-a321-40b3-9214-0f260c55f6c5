{"name": "@microsip/constructs", "version": "0.0.1", "private": true, "description": "Microsip Arquitectura Hibrida Apps Microservice Stack Constructs", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Microsip", "Arquitectura Hibrida"], "license": "ISC", "homepage": ".", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "tsc", "watch": "tsc -w", "test": "echo 'No tests my lord'", "test:ci": "echo 'No tests my lord'", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "peerDependencies": {"aws-cdk-lib": "2.190.0", "constructs": "^10.0.0"}, "devDependencies": {"aws-cdk-lib": "2.190.0", "constructs": "^10.0.0", "rimraf": "^6.0.1", "typescript": "^4.9"}, "dependencies": {"@microsip/ah-create-apps-lambda": "^0.0.1", "@microsip/ah-get-app-by-key-lambda": "^0.0.1", "@microsip/ah-get-app-by-id-lambda": "^0.0.1", "@microsip/ah-get-apps-lambda": "^0.0.1", "@microsip/ah-soft-delete-app-lambda": "^0.0.1", "@microsip/ah-update-apps-lambda": "^0.0.1", "@microsip/util": "^0.0.1", "aws-cdk-lib": "2.190.0", "constructs": "^10.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}