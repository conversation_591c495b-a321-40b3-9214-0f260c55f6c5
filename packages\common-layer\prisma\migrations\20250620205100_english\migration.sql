-- Eliminar índices únicos simples existentes que conflictúan con los compuestos
DROP INDEX IF EXISTS "APLICATIVOS_NOMBRE_key";
DROP INDEX IF EXISTS "APLICATIVOS_CLAVE_key";

-- Crear índice compuesto para mejorar rendimiento de consultas
CREATE INDEX "APLICATIVOS_APLICATIVO_ID_ELIMINADO_idx" ON "APLICATIVOS"("APLICATIVO_ID", "ELIMINADO");

-- Agregar constraints únicos compuestos para soft delete
-- Estos permiten duplicados solo cuando deleted=true
ALTER TABLE "APLICATIVOS"
    ADD CONSTRAINT "unique_name_not_deleted"
    UNIQUE("NOMBRE", "<PERSON><PERSON><PERSON><PERSON>AD<PERSON>");

ALTER TABLE "APLICATIVOS"
    ADD CONSTRAINT "unique_key_not_deleted"
    UNIQUE("CLAVE", "<PERSON>LIM<PERSON>AD<PERSON>");
