-- Eliminar índices únicos simples existentes que conflictúan con los compuestos
DROP INDEX IF EXISTS "APLICATIVOS_NOMBRE_key";
DROP INDEX IF EXISTS "APLICATIVOS_CLAVE_key";

CREATE INDEX "APLICATIVOS_APLICATIVO_ID_ELIMINADO_idx" ON "APLICATIVOS"("APLICATIVO_ID", "ELIMINAD<PERSON>");

ALTER TABLE "APLICATIVOS"
    ADD CONSTRAINT "unique_name_not_deleted"
    UNIQUE("NOMBRE", "ELIMINADO");

ALTER TABLE "APLICATIVOS"
    ADD CONSTRAINT "unique_key_not_deleted"
    UNIQUE("<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>");
