import { APIGatewayProxyResultV2 } from 'aws-lambda'
import { StatusCodes } from 'http-status-codes'
import { response } from './responseUtils'
import { SuccessApiResponse } from './baseResponse'

/**
 * Interfaz para el builder de respuestas exitosas
 */
export interface SuccessResponseBuilder<T = any> {
  setData(data: T): SuccessResponseBuilder<T>
  setMessage(message: string): SuccessResponseBuilder<T>
  build(): APIGatewayProxyResultV2
}

/**
 * Clase base para respuestas exitosas
 */
export abstract class BaseSuccessResponse<T = any> implements SuccessResponseBuilder<T> {
  protected _data?: T
  protected _message: string
  protected _statusCode: number

  constructor(statusCode: number, defaultMessage: string) {
    this._statusCode = statusCode
    this._message = defaultMessage
  }

  setData(data: T): SuccessResponseBuilder<T> {
    this._data = data
    return this
  }

  setMessage(message: string): SuccessResponseBuilder<T> {
    this._message = message
    return this
  }

  build(): APIGatewayProxyResultV2 {
    const body: SuccessApiResponse<T> = {
      success: true,
      message: this._message,
    }

    if (this._data !== undefined) {
      body.data = this._data
    }

    return response({
      statusCode: this._statusCode,
      body,
    })
  }
}

/**
 * Respuesta exitosa para operaciones GET (200 OK)
 */
export class OkResponse<T = any> extends BaseSuccessResponse<T> {
  constructor() {
    super(StatusCodes.OK, 'Operación realizada exitosamente')
  }
}

/**
 * Respuesta exitosa para operaciones POST (201 Created)
 */
export class CreatedResponse<T = any> extends BaseSuccessResponse<T> {
  constructor() {
    super(StatusCodes.CREATED, 'Recurso creado exitosamente')
  }
}

/**
 * Respuesta exitosa para operaciones PUT/PATCH (200 OK)
 */
export class UpdatedResponse<T = any> extends BaseSuccessResponse<T> {
  constructor() {
    super(StatusCodes.OK, 'Recurso actualizado exitosamente')
  }
}

/**
 * Respuesta exitosa para operaciones DELETE (204 No Content)
 */
export class DeletedResponse extends BaseSuccessResponse {
  constructor() {
    super(StatusCodes.NO_CONTENT, 'Recurso eliminado exitosamente')
  }

  build(): APIGatewayProxyResultV2 {
    return response({
      statusCode: this._statusCode,
      body: {
        success: true,
        message: this._message,
      },
    })
  }
}

/**
 * Respuesta exitosa para operaciones de listado (200 OK)
 */
export class ListResponse<T = any> extends BaseSuccessResponse<T[]> {
  constructor() {
    super(StatusCodes.OK, 'Lista obtenida exitosamente')
  }
}

/**
 * Respuesta exitosa para redirecciones permanentes (301 Moved Permanently)
 */
export class MovedPermanentlyResponse extends BaseSuccessResponse {
  constructor() {
    super(StatusCodes.MOVED_PERMANENTLY, 'Recurso movido permanentemente')
  }

  build(): APIGatewayProxyResultV2 {
    return response({
      statusCode: this._statusCode,
      body: {
        success: true,
        message: this._message,
      },
    })
  }
}

/**
 * Respuesta exitosa para redirecciones permanentes (308 Permanent Redirect)
 */
export class PermanentRedirectResponse extends BaseSuccessResponse {
  constructor() {
    super(StatusCodes.PERMANENT_REDIRECT, 'Redirección permanente')
  }

  build(): APIGatewayProxyResultV2 {
    return response({
      statusCode: this._statusCode,
      body: {
        success: true,
        message: this._message,
      },
    })
  }
}

/**
 * Factory para crear respuestas exitosas
 */
export class SuccessResponseFactory {
  /**
   * Crea una respuesta OK (200)
   */
  static ok<T>(data?: T, message?: string): APIGatewayProxyResultV2 {
    const builder = new OkResponse<T>()
    if (data !== undefined) builder.setData(data)
    if (message) builder.setMessage(message)
    return builder.build()
  }

  /**
   * Crea una respuesta Created (201)
   */
  static created<T>(data?: T, message?: string): APIGatewayProxyResultV2 {
    const builder = new CreatedResponse<T>()
    if (data !== undefined) builder.setData(data)
    if (message) builder.setMessage(message)
    return builder.build()
  }

  /**
   * Crea una respuesta Updated (200)
   */
  static updated<T>(data?: T, message?: string): APIGatewayProxyResultV2 {
    const builder = new UpdatedResponse<T>()
    if (data !== undefined) builder.setData(data)
    if (message) builder.setMessage(message)
    return builder.build()
  }

  /**
   * Crea una respuesta Deleted (204)
   */
  static deleted(message?: string): APIGatewayProxyResultV2 {
    const builder = new DeletedResponse()
    if (message) builder.setMessage(message)
    return builder.build()
  }

  /**
   * Crea una respuesta de listado (200)
   */
  static list<T>(data: T[], message?: string): APIGatewayProxyResultV2 {
    const builder = new ListResponse<T>()
    builder.setData(data)
    if (message) builder.setMessage(message)
    return builder.build()
  }

  /**
   * Crea una respuesta de redirección permanente (301)
   */
  static movedPermanently(location: string): APIGatewayProxyResultV2 {
    return response({
      statusCode: StatusCodes.MOVED_PERMANENTLY,
      headers: {
        location,
      },
      body: {
        success: true,
        message: 'Recurso movido permanentemente',
      },
    })
  }

  /**
   * Crea una respuesta de redirección permanente (308)
   */
  static permanentRedirect(location: string): APIGatewayProxyResultV2 {
    return response({
      statusCode: StatusCodes.PERMANENT_REDIRECT,
      headers: {
        location,
      },
      body: {
        success: true,
        message: 'Redirección permanente',
      },
    })
  }

  /**
   * Crea una respuesta personalizada
   */
  static custom<T>(
    statusCode: number,
    data?: T,
    message: string = 'Operación realizada exitosamente'
  ): APIGatewayProxyResultV2 {
    const body: SuccessApiResponse<T> = {
      success: true,
      message,
    }

    if (data !== undefined) {
      body.data = data
    }

    return response({
      statusCode,
      body,
    })
  }
} 