type CharactersMapProps = {
  character: string
  weighing: number
}

const charactersMap: Array<CharactersMapProps> = [
  { character: '0', weighing: 0 },
  { character: '1', weighing: 1 },
  { character: '2', weighing: 2 },
  { character: '3', weighing: 3 },
  { character: '4', weighing: 4 },
  { character: '5', weighing: 5 },
  { character: '6', weighing: 6 },
  { character: '7', weighing: 7 },
  { character: '8', weighing: 8 },
  { character: '9', weighing: 9 },
  { character: 'A', weighing: 10 },
  { character: 'B', weighing: 11 },
  { character: 'C', weighing: 12 },
  { character: 'D', weighing: 13 },
  { character: 'E', weighing: 14 },
  { character: 'F', weighing: 15 },
  { character: 'G', weighing: 16 },
  { character: 'H', weighing: 17 },
  { character: 'I', weighing: 18 },
  { character: 'J', weighing: 19 },
  { character: 'K', weighing: 20 },
  { character: 'L', weighing: 21 },
  { character: 'M', weighing: 22 },
  { character: 'N', weighing: 23 },
  { character: 'O', weighing: 24 },
  { character: 'P', weighing: 25 },
  { character: 'Q', weighing: 26 },
  { character: 'R', weighing: 27 },
  { character: 'S', weighing: 28 },
  { character: 'T', weighing: 29 },
  { character: 'U', weighing: 30 },
  { character: 'V', weighing: 31 },
  { character: 'W', weighing: 32 },
  { character: 'X', weighing: 33 },
  { character: 'Y', weighing: 34 },
  { character: 'Z', weighing: 35 },
]

const charactersDifferentitatorMap: Array<CharactersMapProps> = [
  { character: '0', weighing: 0 },
  { character: '1', weighing: 1 },
  { character: '2', weighing: 2 },
  { character: '3', weighing: 3 },
  { character: '4', weighing: 4 },
  { character: '5', weighing: 5 },
  { character: '6', weighing: 6 },
  { character: '7', weighing: 7 },
  { character: '8', weighing: 8 },
  { character: '9', weighing: 9 },
  { character: 'A', weighing: 10 },
  { character: 'B', weighing: 11 },
  { character: 'C', weighing: 12 },
  { character: 'D', weighing: 13 },
  { character: 'E', weighing: 14 },
  { character: 'F', weighing: 15 },
  { character: 'G', weighing: 16 },
  { character: 'H', weighing: 17 },
  { character: 'I', weighing: 18 },
  { character: 'J', weighing: 19 },
  { character: 'K', weighing: 20 },
  { character: 'L', weighing: 21 },
  { character: 'M', weighing: 22 },
  { character: 'N', weighing: 23 },
  { character: 'O', weighing: 24 },
  { character: 'P', weighing: 25 },
  { character: 'Q', weighing: 26 },
  { character: 'R', weighing: 27 },
  { character: 'S', weighing: 28 },
  { character: 'T', weighing: 29 },
  { character: 'U', weighing: 30 },
  { character: 'V', weighing: 31 },
  { character: 'W', weighing: 32 },
  { character: 'X', weighing: 33 },
  { character: 'Y', weighing: 34 },
  { character: 'Z', weighing: 35 },
]

const checkDigitMap: Array<CharactersMapProps> = [
  { character: '0', weighing: 0 },
  { character: '1', weighing: 1 },
  { character: '2', weighing: 2 },
  { character: '3', weighing: 3 },
  { character: '4', weighing: 4 },
  { character: '5', weighing: 5 },
  { character: '6', weighing: 6 },
  { character: '7', weighing: 7 },
  { character: '8', weighing: 8 },
  { character: '9', weighing: 9 },
  { character: 'A', weighing: 10 },
  { character: 'B', weighing: 11 },
]

const DIFFERENTIATOR_DIVIDER = 35
const INITIAL_CHECK_DIGIT_MULTIPLIER = 2
const MAX_CHECK_DIGIT_MULTIPLIER = 7
const MODULUS = 11
const CHAIN_LENGTH = 14

export const chainToCharactersMap = (chain: string): Array<string> => {
  const chainArray = [...chain]
  const charactersMapChain = chainArray.map(s => {
    const found = charactersMap.find(c => c.character === s)
    if (found) {
      return `${found.weighing}`.padStart(2, '0')
    }
    // Otherwise return 0
    return '00'
  })
  return charactersMapChain
}

export const generateDifferentiator = (chain: string): string => {
  const charactersMapChain = chainToCharactersMap(chain)
  const joinedCharactersMap = charactersMapChain.join('')
  const joinedCharactersMapArray = ['0'].concat([...joinedCharactersMap])
  const multipliedNumbers: Array<number> = []
  // Multiply each pair of elements and an index weighing
  for (let i = 0; i < joinedCharactersMapArray.length - 1; i++) {
    const firstC = joinedCharactersMapArray[i]
    const secondC = joinedCharactersMapArray[i + 1]
    const pair = `${firstC}${secondC}`
    const pairInt = Number.parseInt(pair, 10)
    const multiplier = Number.parseInt(secondC, 10)
    multipliedNumbers.push(pairInt * multiplier * (i + 1))
  }
  // Sum all multiplied pairs
  const finalSum = multipliedNumbers.reduce((sum, x) => sum + x)
  const sumString = `${finalSum}`
  // Retrieve the last 3 characters
  const dividendString = sumString.substr(sumString.length - 3)
  const dividend = Number.parseInt(dividendString, 10)
  const quotient = Math.floor(dividend / DIFFERENTIATOR_DIVIDER)
  const remainder = dividend % DIFFERENTIATOR_DIVIDER
  const cMapA = charactersDifferentitatorMap.find(c => c.weighing === quotient)
  const cMapB = charactersDifferentitatorMap.find(c => c.weighing === remainder)
  const secondC = chain.substr(1, 1)
  const lastC = chain.substr(chain.length - 1, 1)
  return `${cMapA?.character || secondC}${cMapB?.character || lastC}`
}

export const generateCheckDigit = (chain: string): string => {
  const charactersMapChain = chainToCharactersMap(chain)
  const reversedMap = charactersMapChain.reverse()
  let multiplier = INITIAL_CHECK_DIGIT_MULTIPLIER
  const multipliedNumbers: Array<number> = []
  // Multiply each elements and with a Module 11
  for (let i = 0; i < reversedMap.length; i++) {
    // Restore initial value
    if (multiplier > MAX_CHECK_DIGIT_MULTIPLIER) {
      multiplier = INITIAL_CHECK_DIGIT_MULTIPLIER
    }
    const value = reversedMap[i]
    const intValue = Number.parseInt(value, 10)
    multipliedNumbers.push(intValue * multiplier)
    multiplier++
  }
  // Sum all multiplied numbers
  const finalSum = multipliedNumbers.reduce((sum, x) => sum + x)
  const remainder = finalSum % MODULUS
  const digit = MODULUS - remainder
  const digitMap = checkDigitMap.find(c => c.weighing === digit)
  return `${digitMap?.character}`
}

export const enhanceChain = (chain: string): string => {
  const differentiator = generateDifferentiator(chain)
  const digit = generateCheckDigit(`${chain}${differentiator}`)
  return `${chain}${differentiator}${digit}`
}

export enum InvalidReasons {
  IsNull = 'IsNull',
  IsUndefined = 'IsUndefined',
  InvalidLength = 'InvalidLength',
  InvalidDifferentiator = 'InvalidDifferentiator',
  InvalidDigitCheck = 'InvalidDigitCheck',
}

export type IsValidChainResult = {
  valid: boolean
  reason?: InvalidReasons
}

export const isValidChain = (chain: string | undefined | null): IsValidChainResult => {
  if (typeof chain === 'undefined') {
    return {
      valid: false,
      reason: InvalidReasons.IsUndefined,
    }
  }
  if (chain === null) {
    return {
      valid: false,
      reason: InvalidReasons.IsNull,
    }
  }
  if (!chain || chain.length !== CHAIN_LENGTH) {
    return {
      valid: false,
      reason: InvalidReasons.InvalidLength,
    }
  }

  const notEnchanced = chain.substr(0, 11)
  const postfix = chain.substr(11)
  const differentiator = postfix.substr(0, 2)
  const digit = postfix.slice(2)
  const generatedDiff = generateDifferentiator(notEnchanced)
  if (differentiator !== generatedDiff) {
    return {
      valid: false,
      reason: InvalidReasons.InvalidDifferentiator,
    }
  }
  const generatedDigit = generateCheckDigit(`${notEnchanced}${differentiator}`)
  if (digit !== generatedDigit) {
    return {
      valid: false,
      reason: InvalidReasons.InvalidDigitCheck,
    }
  }
  return { valid: true }
}
