// https://stackoverflow.com/questions/55488882/modulenamemapper-settings-in-jest-config-js-doesnt-work-on-circleci
// https://medium.com/ah-technology/a-guide-through-the-wild-wild-west-of-setting-up-a-mono-repo-with-typescript-lerna-and-yarn-ed6a1e5467a
// http://www.guido-flohr.net/lerna-mono-repos-with-internal-dependencies/
// https://medium.com/@NiGhTTraX/how-to-set-up-a-typescript-monorepo-with-lerna-c6acda7d4559

import type { Config } from '@jest/types'
import { pathsToModuleNameMapper } from 'ts-jest/utils'
import tsconfig from './tsconfig.json'

/**
 * @type {import('@jest/types').Config.InitialOptions}
 */
export default async (): Promise<Config.InitialOptions> => {
  return {
    preset: 'ts-jest',
    testMatch: ['**/__tests__/**/*.ts?(x)', '**/?(*.)+(spec|test).ts?(x)'],
    transform: {
      '^.+\\.tsx?$': 'ts-jest',
    },
    moduleNameMapper: pathsToModuleNameMapper(tsconfig?.compilerOptions?.paths, {
      // This has to match the baseUrl defined in tsconfig.json.
      prefix: '<rootDir>',
    }),
  }
}
