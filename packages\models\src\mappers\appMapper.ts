import { Aplicativo as <PERSON>rism<PERSON><PERSON><PERSON>, Prisma } from "@prisma/client";
import { CreateAppRequest, UpdateAppRequest } from "../requests";
import { AppResponse } from "../responses";
import { AppStatus } from "../enums";

export const createAppResponse = (app: PrismaApp): AppResponse => {
    return {
        id: app.id,
        status: app.status as AppStatus,
        name: app.name,
        key: app.key,
        deleted: app.deleted,
        createdBy: app.createdBy,
        dateHourCreate: app.dateHourCreate,
        updatedBy: app.updatedBy,
        dateHourUpdate: app.dateHourUpdate,
        deletedBy: app.deletedBy
    };
};

export const createApp = (request: CreateAppRequest): PrismaApp => {
    return {
        id: 0,
        name: request.name,
        status: AppStatus.ACTIVO,
        key: request.key,
        deleted: false,
        deletedBy: null,
        dateHourCreate: new Date(),
        createdBy: request.createdBy ?? "System",
        dateHourUpdate: null,
        updatedBy: null
    };
};

export const createAppPrismaInput = (request: CreateAppRequest): Prisma.AplicativoCreateInput => {
    const input: Prisma.AplicativoCreateInput = {
        name: request.name,
        key: request.key,
        createdBy: request.createdBy ?? "System"
    };

    if (request.status) {
        input.status = request.status;
    }

    return input;
};

export const updateAppPrismaInput = (request: UpdateAppRequest, username?: string): Prisma.AplicativoUpdateInput => {
    const now = new Date();
    const input: Prisma.AplicativoUpdateInput = {
        updatedBy: username || request.updatedBy || "System",
        dateHourUpdate: now
    };

    if (request.name !== undefined) {
        input.name = request.name;
    }

    if (request.status !== undefined) {
        input.status = request.status;
    }

    return input;
};


