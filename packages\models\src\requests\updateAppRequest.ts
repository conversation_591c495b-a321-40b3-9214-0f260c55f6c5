import {
    Is<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>Option<PERSON>
} from 'class-validator';
import { AppStatus } from '../enums';

export class UpdateAppRequest {
    @IsEnum(AppStatus, { message: 'El estatus debe ser A (Activo) o I (Inactivo)' })
    @IsOptional() 
    status?: AppStatus;

    @IsString({ message: 'El nombre debe ser un string' })
    @IsOptional()
    @MinLength(3, { message: 'El nombre debe tener al menos 3 caracteres' })
    @MaxLength(100, { message: 'El nombre debe tener máximo 100 caracteres' })
    name?: string;

    @IsString()
    @IsOptional()
    updatedBy?: string;

    @IsOptional()
    dateHourUpdate?: Date;
}
