{"name": "@microsip/ah-get-apps-lambda", "version": "0.0.2", "private": true, "description": "Lambda para consultar aplicativos dentro de la Arquitectura Híbrida de Microsip", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["AWS", "CDK", "Serverless", "Lambda", "Microsip", "Arquitectura Hibrida", "Aplicativos"], "license": "ISC", "homepage": ".", "main": ".dist/index.js", "scripts": {"prebuild": "rimraf .dist", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --ci --coverage", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "dependencies": {"@microsip/domain": "^0.0.1", "@microsip/http-layer": "^0.0.1", "@microsip/models": "^0.0.1", "aws-lambda": "^1.0.7"}, "devDependencies": {"@types/aws-lambda": "^8.10.92", "@types/jest": "^29.5.14", "@types/node": "^20.11.30", "jest": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.1.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}