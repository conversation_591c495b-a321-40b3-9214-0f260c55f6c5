generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DB_URL")
}

model Aplicativo {
  id             Int      @id @default(autoincrement()) @map("APLICATIVO_ID")
  status         String   @default("A")                 @map("ESTATUS")
  name           String                              @map("NOMBRE")
  key            String                              @map("CLAVE")
  deleted        <PERSON><PERSON><PERSON>  @default(false)              @map("ELIMINADO")
  deletedBy      String?                             @map("<PERSON>LIMINADO_POR")
  dateHourCreate DateTime @default(now())             @map("FECHA_HORA_CREACION")
  createdBy      String                              @map("CREADO_POR")
  dateHourUpdate DateTime?                           @map("FECHA_HORA_ACTUALIZACION")
  updatedBy      String?                             @map("ACTUALIZADO_POR")

  @@unique([name, deleted], map: "unique_name_not_deleted")
  @@unique([key, deleted],  map: "unique_key_not_deleted")
  @@map("APLICATIVOS")
}
