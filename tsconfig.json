{"extends": "./tsconfig.build.json", "compilerOptions": {"baseUrl": ".", "paths": {"@microsip/util": ["packages/util/src"], "@microsip/constructs": ["packages/constructs/src"], "@microsip/models": ["packages/models/src"], "@microsip/domain": ["packages/domain/src"], "@microsip/lambda-package-utils": ["packages/lambda-package-utils/src"], "@microsip/common-layer": ["packages/common-layer/src"], "@microsip/http-layer": ["packages/http-layer/src"], "@microsip/ah-create-apps-lambda": ["packages/ah-create-apps-lambda/src"], "@microsip/ah-update-apps-lambda": ["packages/ah-update-apps-lambda/src"], "@microsip/ah-get-app-by-id-lambda": ["packages/ah-get-app-by-id-lambda/src"], "@microsip/ah-soft-delete-app-lambda": ["packages/ah-soft-delete-app-lambda/src"], "@microsip/ah-get-app-by-key-lambda": ["packages/ah-get-app-by-key-lambda/src"], "@microsip/ah-get-apps-lambda": ["packages/ah-get-apps-lambda/src"]}, "esModuleInterop": true, "skipLibCheck": true}}