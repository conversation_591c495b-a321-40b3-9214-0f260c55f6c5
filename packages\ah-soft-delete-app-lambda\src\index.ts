import { DeleteAppRequest } from '@microsip/models';
import { deleteAppImpl } from './api';
import { handleAsyncV2, NotFoundException, BadRequestException, ApiResponseManager } from '@microsip/http-layer';

export const deleteApp = handleAsyncV2(async (event) => {
    if (!event?.pathParameters?.id) {
        throw new NotFoundException(
            'ID de aplicativo requerido',
            [{ field: 'id', message: 'El ID del aplicativo es requerido en la URL' }]
        );
    }

    const id = parseInt(event.pathParameters.id);

    if (isNaN(id) || id < 1) {
        throw new BadRequestException(
            'ID de aplicativo inválido',
            [{ field: 'id', message: 'El ID debe ser un número entero mayor a 0' }]
        );
    }

    const request = JSON.parse(event.body || '{}') as DeleteAppRequest;

    const username = event.headers['X-USERNAME'] || event.headers['x-username'];
    if (username && !request.deletedBy) {
        request.deletedBy = username;
    }

    await deleteAppImpl(id, request);
    return ApiResponseManager.deleted('Aplicativo eliminado exitosamente');
});
