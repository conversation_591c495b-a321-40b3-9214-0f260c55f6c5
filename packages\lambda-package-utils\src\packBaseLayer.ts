import * as path from 'path'
import * as fs from 'fs'
import { spawn } from 'child_process'
import * as yazl from 'yazl'
import { glob } from 'glob'
import * as fsExtra from 'fs-extra'
import { dirSync } from 'tmp'
import { checkSumOfFiles } from './checkSum'
import { existsOnS3 } from './existsOnS3'
import { publishToS3 } from './publishToS3'
import { ProgressReporter, ConsoleProgressReporter } from './reporter'

/**
 * Packs a base layer for use with the lambdas with all the dependencies and uploads it to S3
 */
export const packBaseLayer = async ({
  srcDir,
  outDir,
  Bucket,
  reporter,
  layerName,
  lockFileName,
  installCommand,
  onlyCopyNodeModules,
}: {
  srcDir: string
  outDir: string
  Bucket: string
  reporter?: ProgressReporter
  layerName?: string
  /**
   * The name to the lockfile, defaults to "package-lock.json"
   */
  lockFileName?: string
  /**
   * The command (and optionally arguments) used to install the dependencies.
   */
  installCommand?: string[]
  onlyCopyNodeModules?: boolean
}): Promise<string> => {
  const lockFile = onlyCopyNodeModules
    ? path.resolve(srcDir, 'package.json')
    : path.resolve(srcDir, lockFileName ?? 'package-lock.json')
  const hash = (await checkSumOfFiles([lockFile, path.resolve(srcDir, 'lib/**/*')])).checksum

  const name = layerName ?? 'base-layer'
  const zipFilenameWithHash = `${layerName ?? 'base-layer'}-${hash}.zip`
  const localPath = path.resolve(outDir, zipFilenameWithHash)

  const r = reporter ?? ConsoleProgressReporter(name)
  const progress = r.progress(name)
  const success = r.success(name)
  const failure = r.failure(name)
  const sizeInBytes = r.sizeInBytes(name)

  // Check if it already has been built and published
  progress('Checking S3 cache')
  let fileSize = await existsOnS3(Bucket, zipFilenameWithHash, outDir)
  if (fileSize) {
    success('Done')
    sizeInBytes(fileSize)
    return zipFilenameWithHash
  }

  // Check if it already has been built locally
  try {
    progress('Checking local file')
    const { size } = fs.statSync(localPath)
    sizeInBytes(size)
    // File exists
    progress('Publishing to S3', `-> ${Bucket}`)
    await publishToS3(Bucket, zipFilenameWithHash, localPath)
    await existsOnS3(Bucket, zipFilenameWithHash, outDir)
    success('Done')
    return zipFilenameWithHash
  } catch {
    // Pass
  }

  // Check if file exists on S3
  progress('Checking S3 cache')
  fileSize = await existsOnS3(Bucket, zipFilenameWithHash, outDir)
  if (fileSize) {
    success('Done')
    sizeInBytes(fileSize)
    return zipFilenameWithHash
  }

  progress('Packing base layer')

  const tempDir = dirSync({ unsafeCleanup: false }).name
  const installDir = `${tempDir}${path.sep}nodejs`
  fs.mkdirSync(installDir)
  if (!onlyCopyNodeModules) {
    fs.copyFileSync(lockFile, `${installDir}${path.sep}package-lock.json`)
  }
  fs.copyFileSync(path.resolve(srcDir, 'package.json'), `${installDir}${path.sep}package.json`)

  if (!onlyCopyNodeModules) {
    await new Promise<void>((resolve, reject) => {
      progress('Installing dependencies')
      const [cmd, ...args] = installCommand ?? ['npm', 'ci', '--ignore-scripts', '--only=prod']
      const p = spawn(cmd, args, {
        cwd: installDir,
      })
      p.on('close', code => {
        if (code !== 0) {
          const msg = `${cmd} ${args.join(' ')} in ${installDir} exited with code ${code}.`
          failure(msg)
          return reject(new Error(msg))
        }
        success('Dependencies installed')
        return resolve()
      })
      p.stdout.on('data', data => {
        progress('Installing dependencies:', data.toString())
      })
      p.stderr.on('data', data => {
        progress('Installing dependencies:', data.toString())
      })
    })
  }

  if (onlyCopyNodeModules) {
    progress('Copying dependencies')
    await fsExtra.copy(
      path.resolve(srcDir, 'node_modules'),
      `${installDir}${path.sep}node_modules`,
      { 
        recursive: true, 
        dereference: true,
        filter: (src) => {
          // Asegurarse de que se copien los archivos .node y la carpeta .prisma
          const isNodeFile = src.endsWith('.node');
          const isPrismaFolder = src.includes('.prisma');
          const isHiddenFile = src.includes('/.');
          
          // Si es un archivo .node o está en la carpeta .prisma, lo copiamos
          if (isNodeFile || isPrismaFolder) {
            return true;
          }
          
          // Para otros archivos ocultos, los ignoramos a menos que sean .prisma
          if (isHiddenFile && !isPrismaFolder) {
            return false;
          }
          
          return true;
        }
      },
    )
    progress('Copied dependencies')
  }

  if (fs.existsSync(path.resolve(srcDir, 'lib'))) {
    progress('Copying libs')
    await fsExtra.copy(path.resolve(srcDir, 'lib'), `${installDir}${path.sep}lib`, {
      recursive: true,
      dereference: true,
    })
    progress('Copied libs')
  }

  await new Promise<void>(resolve => {
    progress('Creating archive')
    const zipfile = new yazl.ZipFile()
    const files = glob.sync(`${tempDir}${path.sep}**${path.sep}*`)
    files.forEach(file => {
      if (fs.statSync(file).isFile()) {
        zipfile.addFile(file, file.replace(`${tempDir}${path.sep}`, ''))
      }
    })
    zipfile.outputStream.pipe(fs.createWriteStream(localPath)).on('close', () => {
      success('Layer packed', `${Math.round(fs.statSync(localPath).size / 1024)}KB`)
      resolve()
    })
    zipfile.end()
  })

  progress('Publishing to S3', `-> ${Bucket}`)
  await publishToS3(Bucket, zipFilenameWithHash, localPath)
  fileSize = await existsOnS3(Bucket, zipFilenameWithHash, outDir)
  sizeInBytes(fileSize)
  success('All done')

  return zipFilenameWithHash
}
