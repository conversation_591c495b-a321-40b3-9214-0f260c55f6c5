import {
  APIGatewayProxyEventV2,
  APIGatewayProxyHandlerV2,
  APIGatewayProxyResultV2,
  Context,
} from 'aws-lambda'
// import { v4 as uuid } from 'uuid'
import { Item } from './models'
import { plainToClassFromExist } from 'class-transformer'
import { validate, ValidationError } from 'class-validator'
import {
  NotFoundException,
  NoBodyProvidedException,
} from './exceptions'
import { ApiResponseManager } from './response/apiResponseManager'
import { ErrorMiddlewareFactory } from './middleware'

export const handleAsyncV2 = (
  handler: (_event: APIGatewayProxyEventV2, _ctx: Context) => Promise<APIGatewayProxyResultV2>,
): APIGatewayProxyHandlerV2 => {
  return async (
    event: APIGatewayProxyEventV2,
    context: Context,
  ): Promise<APIGatewayProxyResultV2> => {
    console.log('handleAsyncV2')
    console.log({ event: JSON.stringify(event, null, 2) })
    console.log({ requestContext: JSON.stringify(event?.requestContext, null, 2) })
    console.log({ context: JSON.stringify(context, null, 2) })
    try {
      return await handler(event, context)
    } catch (e: any) {
      console.error('Unhandled Error:', e)
      
      // Usar el middleware de errores para manejar la excepción
      const errorMiddleware = ErrorMiddlewareFactory.get('ValidationError')
      if (errorMiddleware && Array.isArray(e) && e.length > 0 && 'constraints' in e[0]) {
        // Es un error de validación, usar el middleware específico
        return errorMiddleware.handle(e, event, context)
      }
      
      // Para otros tipos de errores, usar el middleware genérico
      const genericMiddleware = ErrorMiddlewareFactory.get('DatabaseError')
      if (genericMiddleware) {
        return genericMiddleware.handle(e, event, context)
      }
      
      // Fallback al handler genérico si no hay middleware disponible
      const { exceptionHandler } = await import('./exceptions')
      return exceptionHandler.handle(e)
    }
  }
}

export const initializeItem = <T extends Item>(item: T, ctx: Context): T => {
  const currentDate = new Date()
  currentDate.setHours(currentDate.getHours() - 6)

  item.dateHourCreate = currentDate
  item.createdBy = ctx.identity?.cognitoIdentityId || ""
  item.dateHourUpdate = currentDate
  item.updatedBy = ctx.identity?.cognitoIdentityId || ""

  return item
}

export const initializeItemNoContext = <T extends Item>(item: T, username: string): T => {
  const currentDate = new Date()
  currentDate.setHours(currentDate.getHours() - 6)

  item.dateHourCreate = currentDate
  item.createdBy = item.createdBy || username
  item.dateHourUpdate = currentDate
  item.updatedBy = item.updatedBy || username
  item.deleted = item.deleted === undefined ? false : item.deleted

  return item
}

export const createItem = async <T extends Item>(
  example: new () => T,
  ctx: Context,
  event: APIGatewayProxyEventV2,
): Promise<T> => {
  if (!event.body) {
    throw new NoBodyProvidedException()
  }

  const newItem = new example()
  let itemTransformed = plainToClassFromExist<Item, object>(newItem, JSON.parse(event.body))

  console.log({ itemTransformed })

  const errors: Array<ValidationError> = await validate(itemTransformed)
  console.log({ errors })
  if (errors && errors.length > 0) {
    // Lanzar los errores de validación directamente para que el middleware los maneje
    throw errors
  }

  itemTransformed = initializeItem(itemTransformed, ctx)
  const username =
    event.headers['X-USERNAME'] || event.headers['x-username']
  itemTransformed.createdBy = username || itemTransformed.createdBy
  itemTransformed.updatedBy = username || itemTransformed.updatedBy

  return itemTransformed as T
}

export const createItemNoItem = async <T>(
  example: (new () => T),
  event: APIGatewayProxyEventV2,
): Promise<T> => {
  if (!event.body) {
    throw new NoBodyProvidedException()
  }

  const newItem = new example()
  let itemTransformed = plainToClassFromExist<T, object>(newItem, JSON.parse(event.body))

  console.log({ itemTransformed })

  const errors: Array<ValidationError> = await validate(itemTransformed as object)
  console.log({ errors })
  if (errors && errors.length > 0) {
    // Lanzar los errores de validación directamente para que el middleware los maneje
    throw errors
  }

  return itemTransformed as T
}

export const updateItem = async <T extends Item>(
  example: (new () => T) & Item,
  ctx: Context,
  event: APIGatewayProxyEventV2,
): Promise<T> => {
  if (!event.body) {
    throw new NoBodyProvidedException()
  }

  const newItem = new example()
  let itemTransformed = plainToClassFromExist<Item, object>(newItem, JSON.parse(event.body))
  console.log({ itemTransformed })

  const errors: Array<ValidationError> = await validate(itemTransformed)
  console.log({ errors })
  if (errors && errors.length > 0) {
    // Lanzar los errores de validación directamente para que el middleware los maneje
    throw errors
  }

  itemTransformed = initializeItem(itemTransformed, ctx)
  const username =
  event.headers['X-USERNAME'] || event.headers['x-username']
  itemTransformed.updatedBy =  username || itemTransformed.updatedBy

  return itemTransformed as T
}

export const restPostApiInvocation = <T extends Item, R>(
  modelExample: new () => T,
  saveFunction: (item: T) => Promise<R>,
  successMessage?: string,
): APIGatewayProxyHandlerV2 =>
  handleAsyncV2(async (event, ctx) => {
    const item = await createItem(modelExample, ctx, event)
    const savedItem = await saveFunction(item as T)
    return ApiResponseManager.created(savedItem, successMessage)
  })

export const restPutApiInvocation = <T extends Item>(
  modelExample: (new () => T) & Item,
  updateFunction: (id: string, item: T) => Promise<T>,
  successMessage?: string,
): APIGatewayProxyHandlerV2 =>
  handleAsyncV2(async (event, ctx) => {
    if (!event?.pathParameters?.id) throw new NotFoundException()
    const item = await updateItem(modelExample, ctx, event)
    const savedItem = await updateFunction(event.pathParameters.id, item as T)
    return ApiResponseManager.updated(savedItem, successMessage)
  })

export const restGetListApiInvocation = <T extends Item>(
  listFunction: () => Promise<Array<T>>,
  successMessage?: string,
): APIGatewayProxyHandlerV2 =>
  handleAsyncV2(async () => {
    const items = await listFunction()
    return items ? ApiResponseManager.list(items, successMessage) : ApiResponseManager.successNoData()
  })

export const restGetItemApiInvocation = <T extends Item>(
  getFunction: (id: string) => Promise<T>,
  successMessage?: string,
): APIGatewayProxyHandlerV2 =>
  handleAsyncV2(async event => {
    if (!event?.pathParameters?.id) throw new NotFoundException()
    const item = await getFunction(event.pathParameters.id)
    return item ? ApiResponseManager.list([item], successMessage) : ApiResponseManager.successNoData()
  })

export const restPostApiInvocationWithDifferentTypes = <TInput, TOutput>(
  inputModelExample: (new () => TInput),
  saveFunction: (item: TInput) => Promise<TOutput>,
  successMessage?: string,
): APIGatewayProxyHandlerV2 =>
  handleAsyncV2(async (event) => {
    const item = await createItemNoItem(inputModelExample, event)
    const savedItem = await saveFunction(item)
    return ApiResponseManager.created(savedItem, successMessage)
  })
