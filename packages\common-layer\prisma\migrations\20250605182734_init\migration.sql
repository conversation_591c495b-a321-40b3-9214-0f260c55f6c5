-- CreateTable
CREATE TABLE "APLICATIVOS" (
    "APLICATIVO_ID" SERIAL NOT NULL,
    "ESTATUS" TEXT NOT NULL DEFAULT 'A',
    "NOMBRE" TEXT NOT NULL,
    "CLAVE" TEXT NOT NULL,
    "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>" BOOLEAN NOT NULL DEFAULT false,
    "<PERSON><PERSON><PERSON><PERSON>ADO_POR" TEXT,
    "FECHA_HORA_CREACION" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CREADO_POR" TEXT NOT NULL,
    "FECHA_HORA_ACTUALIZACION" TIMESTAMP(3),
    "ACTUALIZADO_POR" TEXT,

    CONSTRAINT "APLICATIVOS_pkey" PRIMARY KEY ("APLICATIVO_ID")
);

-- CreateIndex
CREATE UNIQUE INDEX "APLICATIVOS_NOMBRE_key" ON "APLICATIVOS"("NOMBRE");

-- C<PERSON>Index
CREATE UNIQUE INDEX "APLICATIVOS_CLAVE_key" ON "APLICATIVOS"("CLAVE");

-- <PERSON><PERSON>Index
CREATE INDEX "APLICATIVOS_APLICATIVO_ID_idx" ON "APLICATIVOS"("APLICATIVO_ID");
