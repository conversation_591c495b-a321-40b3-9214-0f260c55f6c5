import { AppResponse, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';
import { NotFoundException, ConflictException } from '@microsip/http-layer';

const appsRepository = new AppsRepository();

export async function deleteAppById(id: number, deletedBy: string): Promise<AppResponse> {
    const existingApp = await appsRepository.findById(id);

    if (!existingApp) {
        throw new NotFoundException(
            `No existe un aplicativo con el ID ${id}`,
            [{ field: 'id', message: `No se encontró un aplicativo con el ID: ${id}` }]
        );
    }

    if (existingApp.deleted) {
        throw new ConflictException(
            `El aplicativo con ID ${id} ya fue eliminado`,
            [{ field: 'id', message: `El aplicativo con ID: ${id} ya no está disponible` }]
        );
    }

    const deletedApp = await appsRepository.softDelete(id, deletedBy);

    return createAppResponse(deletedApp);
}
