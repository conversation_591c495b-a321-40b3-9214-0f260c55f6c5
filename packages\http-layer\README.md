# `@microsip/http-layer`

> A Module for Http Functions Lambda Layer Microsip Arquitectura Hibrida

# HTTP Layer - Sistema Unificado de Respuestas

## Arquitectura del Sistema

Este sistema está diseñado siguiendo los principios SOLID y las mejores prácticas de arquitectura limpia, proporcionando una estructura unificada para todas las respuestas de la API.

### Estructura de Archivos

```
src/
├── response/
│   ├── baseResponse.ts              # Interfaces base para todas las respuestas
│   ├── successResponse.ts           # Sistema de respuesta exitosa
│   ├── apiResponseManager.ts        # Manager unificado de respuestas
│   └── index.ts                     # Exportaciones
├── exceptions/
│   ├── applicationException.ts      # Clase base para todas las excepciones
│   ├── exceptionHandler.ts          # Manejador principal de excepciones
│   └── ...                         # Otras excepciones específicas
├── middleware/
│   └── errorMiddleware.ts           # Middlewares específicos para tipos de error
└── apiGateway.ts                    # Funciones de respuesta HTTP
```

## Principios SOLID Aplicados

### 1. Single Responsibility Principle (SRP)
- **ApiResponseManager**: Solo maneja la creación de respuestas
- **ExceptionHandler**: Solo se encarga de manejar excepciones
- **ErrorMiddleware**: Solo se encarga de lógica específica por tipo de error
- **SuccessResponse**: Solo se encarga de formatear respuestas exitosas
- **ApplicationException**: Solo define la estructura base de excepciones

### 2. Open/Closed Principle (OCP)
- El sistema está abierto para extensión (nuevos tipos de excepciones y respuestas)
- Cerrado para modificación (no necesitas cambiar código existente)

### 3. Liskov Substitution Principle (LSP)
- Todas las excepciones pueden ser sustituidas por ApplicationException
- Todas las respuestas exitosas pueden ser sustituidas por BaseSuccessResponse
- Todos los middlewares implementan la misma interfaz

### 4. Interface Segregation Principle (ISP)
- Interfaces específicas para cada responsabilidad
- `BaseApiResponse`: Interfaz base para todas las respuestas
- `SuccessApiResponse`: Interfaz para respuestas exitosas
- `ErrorApiResponse`: Interfaz para respuestas de error
- `ExceptionHandler`: Interfaz para manejo de excepciones
- `ErrorMiddleware`: Interfaz para lógica específica
- `SuccessResponseBuilder`: Interfaz para construcción de respuestas

### 5. Dependency Inversion Principle (DIP)
- Dependemos de abstracciones (interfaces) no de implementaciones concretas
- El `ExceptionHandlerManager` coordina diferentes handlers
- El `SuccessResponseFactory` coordina diferentes tipos de respuesta
- El `ApiResponseManager` proporciona una interfaz unificada

## Estructura Unificada de Respuestas

### Formato Estándar para Respuestas Exitosas

```json
{
  "success": true,
  "data": {
    "id": "abc123",
    "name": "Nuevo objeto"
  },
  "message": "Recurso creado exitosamente"
}
```

### Formato Estándar para Respuestas de Error

```json
{
  "success": false,
  "error": {
    "code": 404,
    "message": "El usuario con ID 123 no fue encontrado.",
    "status": "NOT_FOUND",
    "details": [
      { "field": "email", "message": "Formato inválido" },
      { "field": "password", "message": "Debe tener al menos 8 caracteres" }
    ]
  }
}
```

## Sistema de Respuesta Exitosa

### Funciones de Respuesta Disponibles

#### 1. Respuesta OK (200)
```typescript
import { ok } from '@microsip/http-layer'

// Con datos
return ok(userData, 'Usuario obtenido exitosamente')

// Sin datos
return ok(undefined, 'Operación completada')
```

#### 2. Respuesta Created (201)
```typescript
import { created } from '@microsip/http-layer'

return created(newUser, 'Usuario creado exitosamente')
```

#### 3. Respuesta Updated (200)
```typescript
import { updated } from '@microsip/http-layer'

return updated(updatedUser, 'Usuario actualizado exitosamente')
```

#### 4. Respuesta Deleted (204)
```typescript
import { deleted } from '@microsip/http-layer'

return deleted('Usuario eliminado exitosamente')
```

#### 5. Respuesta List (200)
```typescript
import { list } from '@microsip/http-layer'

return list(users, 'Lista de usuarios obtenida exitosamente')
```

### ApiResponseManager - Interfaz Unificada

```typescript
import { ApiResponseManager } from '@microsip/http-layer'

// Respuesta exitosa con datos
return ApiResponseManager.success(user, 'Usuario obtenido exitosamente')

// Respuesta exitosa sin datos
return ApiResponseManager.successNoData('Operación completada')

// Respuesta de error
return ApiResponseManager.genericError('Mensaje de error', 400, details)

// Validación de respuestas
if (ApiResponseManager.isSuccess(response)) {
  // Manejar respuesta exitosa
}

if (ApiResponseManager.isError(response)) {
  // Manejar respuesta de error
}
```

### Builder Pattern

También puedes usar el Builder Pattern para mayor flexibilidad:

```typescript
import { OkResponse, CreatedResponse } from '@microsip/http-layer'

// Usando Builder Pattern
return new OkResponse<User>()
  .setData(user)
  .setMessage('Usuario obtenido exitosamente')
  .build()

// O para creación
return new CreatedResponse<User>()
  .setData(newUser)
  .setMessage('Usuario creado exitosamente')
  .build()
```

### Factory Pattern

Para casos especiales, puedes usar el Factory:

```typescript
import { SuccessResponseFactory } from '@microsip/http-layer'

// Respuesta personalizada
return SuccessResponseFactory.custom(
  200,
  customData,
  'Operación personalizada completada'
)
```

## Sistema de Manejo de Excepciones

### Uso del Sistema de Excepciones

```typescript
import { 
  BadRequestException, 
  NotFoundException,
  exceptionHandler 
} from '@microsip/http-layer'

// En tu lambda
try {
  // Tu lógica aquí
  if (!user) {
    throw new NotFoundException('El usuario con ID 123 no fue encontrado')
  }
} catch (error) {
  return exceptionHandler.handle(error)
}
```

### Excepciones con Detalles

```typescript
import { BadRequestException } from '@microsip/http-layer'

const details = [
  { field: 'email', message: 'Formato inválido' },
  { field: 'password', message: 'Debe tener al menos 8 caracteres' }
]

throw new BadRequestException('Los datos proporcionados son inválidos', details)
```

## Tipos de Excepciones Disponibles

| Excepción | Código HTTP | Status (automático) | Mensaje por defecto |
|-----------|-------------|---------------------|---------------------|
| `BadRequestException` | 400 | BAD_REQUEST | "Solicitud incorrecta" |
| `UnauthorizedException` | 401 | UNAUTHORIZED | "No autorizado" |
| `NotFoundException` | 404 | NOT_FOUND | "Recurso no encontrado" |
| `ConflictException` | 409 | CONFLICT | "Conflicto en los datos" |
| `InvalidBodyException` | 406 | NOT_ACCEPTABLE | "Cuerpo de la solicitud inválido" |
| `NoBodyProvidedException` | 406 | NOT_ACCEPTABLE | "No se proporcionó cuerpo en la solicitud" |
| `InternalServerException` | 500 | INTERNAL_SERVER_ERROR | "Error interno del servidor" |

## Ejemplo Completo de Uso

```typescript
import { 
  handleAsyncV2,
  ok,
  created,
  BadRequestException,
  NotFoundException,
  ApiResponseManager
} from '@microsip/http-layer'

interface User {
  id: string
  name: string
  email: string
}

// GET - Obtener usuario
export const getUser = handleAsyncV2(async (event) => {
  const userId = event.pathParameters?.id
  
  if (!userId) {
    throw new BadRequestException('ID de usuario requerido')
  }

  const user: User = {
    id: userId,
    name: 'Juan Pérez',
    email: '<EMAIL>'
  }

  if (!user) {
    throw new NotFoundException(`Usuario con ID ${userId} no encontrado`)
  }

  return ok(user, 'Usuario obtenido exitosamente')
})

// POST - Crear usuario
export const createUser = handleAsyncV2(async (event) => {
  const userData = JSON.parse(event.body || '{}')
  
  if (!userData.name || !userData.email) {
    const details = [
      { field: 'name', message: 'El nombre es requerido' },
      { field: 'email', message: 'El email es requerido' }
    ]
    throw new BadRequestException('Datos de usuario incompletos', details)
  }

  const newUser: User = {
    id: 'user-123',
    name: userData.name,
    email: userData.email
  }

  return created(newUser, 'Usuario creado exitosamente')
})

// Health Check - Sin datos
export const healthCheck = handleAsyncV2(async () => {
  return ApiResponseManager.successNoData('Servicio funcionando correctamente')
})
```

## Ventajas del Sistema Unificado

1. **Consistencia Total**: Todas las respuestas siguen el mismo formato base
2. **Separación de Responsabilidades**: Cada componente tiene una responsabilidad específica
3. **Extensibilidad**: Fácil agregar nuevos tipos de excepciones y respuestas
4. **Testabilidad**: Cada componente puede ser testeado de forma independiente
5. **Mantenibilidad**: Código más limpio y fácil de mantener
6. **Reutilización**: Los middlewares pueden ser reutilizados en diferentes contextos
7. **Mensajes Personalizados**: Puedes proporcionar mensajes descriptivos y específicos
8. **Status Automático**: El status se genera automáticamente basado en el código HTTP
9. **Flexibilidad**: Múltiples formas de crear respuestas (funciones, builder, factory, manager)
10. **Tipado Fuerte**: Soporte completo de TypeScript con genéricos
11. **Validación de Tipos**: Funciones helper para validar el tipo de respuesta
12. **Interfaz Unificada**: Un solo punto de entrada para todas las respuestas

## Migración desde el Sistema Anterior

El sistema es compatible hacia atrás. Solo necesitas:

1. Importar las nuevas funciones de respuesta
2. Usar el nuevo formato de constructores de excepciones
3. Opcionalmente, usar el ApiResponseManager para mayor consistencia

```typescript
// Antes
return {
  statusCode: 200,
  body: JSON.stringify({
    success: true,
    data: user,
    message: 'Usuario obtenido'
  })
}

// Ahora
return ok(user, 'Usuario obtenido exitosamente')

// O con el manager unificado
return ApiResponseManager.success(user, 'Usuario obtenido exitosamente')
```

## Estructura de Respuestas por Tipo

### Respuestas Exitosas
- **GET**: `{ success: true, data: {...}, message: "..." }`
- **POST**: `{ success: true, data: {...}, message: "..." }`
- **PUT/PATCH**: `{ success: true, data: {...}, message: "..." }`
- **DELETE**: `{ success: true, message: "..." }`
- **LIST**: `{ success: true, data: [...], message: "..." }`

### Respuestas de Error
- **Todas**: `{ success: false, error: { code, message, status, details } }`

Esta estructura unificada garantiza consistencia en toda tu API y facilita el manejo de respuestas en el frontend.

# HTTP Layer Package

Este paquete proporciona una capa de abstracción para manejar respuestas HTTP y excepciones en AWS Lambda con API Gateway v2.

## Características

- **Manejo unificado de respuestas**: Sistema consistente para respuestas exitosas y errores
- **Middleware de errores integrado**: Manejo centralizado de diferentes tipos de errores
- **Validación automática**: Los errores de validación se manejan automáticamente
- **Logging contextual**: Los errores incluyen información del contexto de la request
- **Extensibilidad**: Fácil agregar nuevos tipos de middleware y handlers

## Estructura de Respuestas

### Respuestas Exitosas
```json
{
  "success": true,
  "data": { ... },
  "message": "Operación exitosa"
}
```

### Respuestas de Error
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Bad Request",
    "status": "BAD_REQUEST",
    "details": [
      {
        "field": "email",
        "message": "Invalid email format"
      }
    ]
  }
}
```

## Uso Básico

### Manejo de Errores Automático

El middleware de errores está **integrado automáticamente** en `handleAsyncV2`. No necesitas configurar nada:

```typescript
import { handleAsyncV2 } from '@microsip/http-layer'
import { ApiResponseManager } from '@microsip/http-layer'
import { UserRegisterRequest } from '@microsip/models'

export const registerUser = handleAsyncV2(async (event, context) => {
  // Los errores de validación se manejan automáticamente
  const userRequest = await createItem(UserRegisterRequest, context, event)
  
  // Tu lógica de negocio aquí
  const savedUser = await userService.save(userRequest)
  
  return ApiResponseManager.created(savedUser)
})
```

### Tipos de Errores Manejados Automáticamente

1. **Errores de Validación** (`ValidationError[]`)
   - Se convierten automáticamente a `InvalidBodyException`
   - Incluyen detalles de los campos con errores
   - Logging contextual con información de la request

2. **Errores de Base de Datos**
   - Logging específico con stack trace
   - Información del contexto de la request

3. **Errores de Autenticación**
   - Logging de intentos de acceso no autorizado
   - Información de headers y contexto

4. **Errores Genéricos**
   - Fallback al handler genérico
   - Respuesta consistente

## Middleware de Errores

### Middlewares Disponibles

- **ValidationErrorMiddleware**: Maneja errores de validación de class-validator
- **DatabaseErrorMiddleware**: Maneja errores de base de datos
- **AuthErrorMiddleware**: Maneja errores de autenticación/autorización

### Logging Contextual

Los middlewares incluyen automáticamente información contextual:

```typescript
// Ejemplo de log de error de validación
console.warn('Validation Error:', {
  details: [
    { field: 'email', message: 'Invalid email format' }
  ],
  event: '/users/register',
  context: 'aws-request-id-123',
  headers: { 'content-type': 'application/json' }
})
```

### Extensibilidad

Puedes registrar nuevos middlewares:

```typescript
import { ErrorMiddlewareFactory } from '@microsip/http-layer'

class CustomErrorMiddleware implements ErrorMiddleware {
  handle(error: Error | ValidationError[], event: APIGatewayProxyEventV2, context: Context) {
    // Tu lógica personalizada aquí
    return exceptionHandler.handle(error as Error)
  }
}

ErrorMiddlewareFactory.register('CustomError', new CustomErrorMiddleware())
```

## Funciones de Respuesta

### ApiResponseManager

```typescript
import { ApiResponseManager } from '@microsip/http-layer'

// Respuestas exitosas
ApiResponseManager.success(data, message)           // 200 OK
ApiResponseManager.created(data, message)           // 201 Created
ApiResponseManager.updated(data, message)           // 200 OK
ApiResponseManager.list(items, message)             // 200 OK
ApiResponseManager.successNoData(message)           // 200 OK (sin datos)
```

### Funciones de API Gateway

```typescript
import { 
  restPostApiInvocation, 
  restPutApiInvocation,
  restGetListApiInvocation,
  restGetItemApiInvocation 
} from '@microsip/http-layer'

// POST /users
export const createUser = restPostApiInvocation(
  UserRegisterRequest,
  userService.save
)

// PUT /users/{id}
export const updateUser = restPutApiInvocation(
  UserRegisterRequest,
  userService.update
)

// GET /users
export const listUsers = restGetListApiInvocation(
  userService.findAll
)

// GET /users/{id}
export const getUser = restGetItemApiInvocation(
  userService.findById
)
```

## Beneficios de la Nueva Implementación

### 1. Código Más Limpio
- **Antes**: Bloques de validación repetitivos en cada función
- **Ahora**: Validación automática centralizada

### 2. Manejo Unificado
- **Antes**: Diferentes formas de manejar errores
- **Ahora**: Un solo flujo para todos los tipos de error

### 3. Logging Mejorado
- **Antes**: Logs básicos sin contexto
- **Ahora**: Logs con información completa de la request

### 4. Extensibilidad
- **Antes**: Difícil agregar nuevos tipos de manejo de errores
- **Ahora**: Fácil registro de nuevos middlewares

### 5. Consistencia
- **Antes**: Respuestas de error inconsistentes
- **Ahora**: Formato consistente para todos los errores

## Ejemplos

Ver `examples/middlewareExample.ts` para ejemplos completos de uso.

## Principios SOLID Aplicados

- **Single Responsibility**: Cada middleware maneja un tipo específico de error
- **Open/Closed**: Fácil extensión sin modificar código existente
- **Liskov Substitution**: Todos los middlewares implementan la misma interfaz
- **Interface Segregation**: Interfaces específicas para cada tipo de middleware
- **Dependency Inversion**: Dependencia de abstracciones, no implementaciones
