import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsGetApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
    idParamName?: string;
};

/** Builder que añade GET /apps/{id} */
export const buildAppsGetByIdApi = (
    scope: Construct,
    props: AppsGetApiProps,
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod',
        idParamName = 'id',
    } = props;

    const getFunction = new lambda.Function(scope, 'AppsGetByIdFunction', {
        functionName: 'ah-get-app-by-id-lambda',
        description: `GET API handler for apps/{${idParamName}} in ${stage} environment`,
        code,
        handler: 'index.getAppById',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('GET', new api.LambdaIntegration(getFunction), {
        authorizationType: api.AuthorizationType.NONE,
        operationName: 'getAppById',
    });
};
