import { AppResponse, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';
import { NotFoundException } from '@microsip/http-layer';

const appsRepository = new AppsRepository();

export async function getAppById(id: number): Promise<AppResponse> {
    const app = await appsRepository.findById(id);

    // Validar que el aplicativo exista y no esté eliminado
    if (!app || app.deleted) {
        throw new NotFoundException(
            `No existe un aplicativo con el ID ${id}`,
            [{ field: 'id', message: `No se encontró un aplicativo con el ID: ${id}` }]
        );
    }

    return createAppResponse(app);
}
