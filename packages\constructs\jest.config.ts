import type { Config } from '@jest/types'
import { pathsToModuleNameMapper } from 'ts-jest'
import tsconfig from './tsconfig.json'

/**
 * @type {import('@jest/types').Config.InitialOptions}
 */
export default async (): Promise<Config.InitialOptions> => {
  return {
    preset: 'ts-jest',
    testMatch: ['**/__tests__/**/*.ts?(x)', '**/?(*.)+(spec|test).ts?(x)'],
    transform: {
      '^.+\\.tsx?$': 'ts-jest',
    },
    moduleNameMapper: pathsToModuleNameMapper(tsconfig?.compilerOptions?.paths, {
      // This has to match the baseUrl defined in tsconfig.json.
      prefix: '<rootDir>',
    }),
  }
}
