{
    "extends": "../../tsconfig.json",
    "compilerOptions": {
        "baseUrl": ".",
        "paths": {
            "@microsip/models" : ["../models/src/"],
            "@microsip/domain" : ["../domain/src/"],
            "@microsip/util": ["../util/src"],
            "@microsip/http-layer": ["../http-layer/src/"]
        },
        "outDir": ".dist",
        "strict": true,
        "declaration": true,
        "skipLibCheck": true,
        "noUnusedParameters": true,
        "noUnusedLocals": true,
        "noImplicitThis": true,
        "noImplicitReturns": true,
        "removeComments": true,
        "noFallthroughCasesInSwitch": true,
        "forceConsistentCasingInFileNames": true,
        "noImplicitAny": true,
    },
    "include": [
        "src/**/*"
    ],
    "exclude": [
        ".dist",
        "jest.config.ts"
    ]
}