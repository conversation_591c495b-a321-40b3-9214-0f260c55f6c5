import { getAppByKeyImpl } from './api';
import { handleAsyncV2, NotFoundException, BadRequestException, ApiResponseManager } from '@microsip/http-layer';

export const getAppByKey = handleAsyncV2(async (event) => {
    if (!event?.pathParameters?.key) {
        throw new NotFoundException(
            'Clave de aplicativo requerida',
            [{ field: 'key', message: 'La clave del aplicativo es requerida en la URL' }]
        );
    }

    const key = event.pathParameters.key;

    // Validar formato de clave (debe seguir el patrón ABC-DEFGH)
    const keyPattern = /^[A-Z]+-[A-Z]{1,5}$/;
    if (!keyPattern.test(key)) {
        throw new BadRequestException(
            'Formato de clave inválido',
            [{ field: 'key', message: 'La clave debe tener el formato: 3 letras MAYÚSCULAS, seguidas de un guión (-), seguidas de 1 a 5 letras MAYÚSCULAS. Ejemplo: ABC-DEFGH' }]
        );
    }

    const result = await getAppByKeyImpl(key);
    return ApiResponseManager.success(result, 'Aplicativo obtenido exitosamente');
});
