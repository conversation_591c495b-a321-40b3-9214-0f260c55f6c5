import { UpdateAppRequest } from '@microsip/models';
import { updateAppImpl } from './api';
import { handleAsyncV2, NotFoundException, BadRequestException, createItemNoItem, ApiResponseManager } from '@microsip/http-layer';

export const updateApp = handleAsyncV2(async (event) => {
    if (!event?.pathParameters?.id) {
        throw new NotFoundException(
            'ID de aplicativo requerido',
            [{ field: 'id', message: 'El ID del aplicativo es requerido en la URL' }]
        );
    }

    const id = parseInt(event.pathParameters.id);

    if (isNaN(id) || id < 1) {
        throw new BadRequestException(
            'ID de aplicativo inválido',
            [{ field: 'id', message: 'El ID debe ser un número entero mayor a 0' }]
        );
    }

    const item = await createItemNoItem(UpdateAppRequest, event);

    const requestBody = JSON.parse(event.body || '{}');
    if (requestBody.key !== undefined) {
        throw new BadRequestException(
            'La clave del aplicativo no se puede modificar',
            [{ field: 'key', message: 'La clave (key) es inmutable y no puede ser actualizada' }]
        );
    }

    if (!item.name && item.status === undefined) {
        throw new BadRequestException(
            'Debe proporcionar al menos un campo para actualizar',
            [{ field: 'body', message: 'Se requiere al menos el campo "name" o "status" para realizar la actualización' }]
        );
    }

    const username = event.headers['X-USERNAME'] || event.headers['x-username'];
    if (username) {
        item.updatedBy = username;
    }

    const savedItem = await updateAppImpl(id, item);
    return ApiResponseManager.updated(savedItem, 'Aplicativo actualizado exitosamente');
});