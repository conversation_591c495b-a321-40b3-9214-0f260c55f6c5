import { APIGatewayProxyEventV2, APIGatewayProxyResultV2, Context } from 'aws-lambda'
import { ValidationError } from 'class-validator'
import { exceptionHandler } from '../exceptions/exceptionHandler'
import { BadRequestException } from '../exceptions'

/**
 * Interfaz para el middleware de manejo de errores
 */
export interface ErrorMiddleware {
  handle(
    error: Error | ValidationError[],
    event: APIGatewayProxyEventV2,
    context: Context
  ): APIGatewayProxyResultV2
}

/**
 * Middleware para manejar errores de validación
 */
export class ValidationErrorMiddleware implements ErrorMiddleware {
  handle(
    error: Error | ValidationError[],
    event: APIGatewayProxyEventV2,
    context: Context
  ): APIGatewayProxyResultV2 {
    // Verificar si el error es un array de ValidationError
    if (Array.isArray(error) && error.length > 0 && 'constraints' in error[0]) {
      const validationErrors = error as ValidationError[]
      const details = this.mapValidationErrors(validationErrors)
      const validationException = new BadRequestException("Errores de validación", details)

      // Log del error de validación con contexto
      console.warn('Validation Error:', {
        details,
        event: event.rawPath,
        context: context.awsRequestId,
        headers: event.headers
      })

      return exceptionHandler.handle(validationException)
    }

    // Si es una excepción de validación ya creada
    if (error instanceof BadRequestException) {
      console.warn('Validation Exception:', {
        message: error.message,
        details: error.details,
        event: event.rawPath,
        context: context.awsRequestId
      })
      return exceptionHandler.handle(error)
    }
    
    // Si no es un error de validación, delegar al handler genérico
    return exceptionHandler.handle(error as Error)
  }

  private mapValidationErrors(errors: ValidationError[]): Array<{ field: string; message: string }> {
    const details: Array<{ field: string; message: string }> = []
    errors.forEach(err => {
      if (err.constraints) {
        Object.values(err.constraints).forEach(msg => {
          details.push({
            field: err.property,
            message: msg
          })
        })
      }
      if (err.children && err.children.length > 0) {
        details.push(...this.mapValidationErrors(err.children))
      }
    })
    return details
  }
}

/**
 * Middleware para manejar errores de base de datos
 */
export class DatabaseErrorMiddleware implements ErrorMiddleware {
  handle(
    error: Error | ValidationError[],
    event: APIGatewayProxyEventV2,
    context: Context
  ): APIGatewayProxyResultV2 {
    // Aquí puedes agregar lógica específica para errores de base de datos
    // Por ejemplo, logging específico, métricas, etc.
    console.error('Database Error:', {
      error: (error as Error).message,
      stack: (error as Error).stack,
      event: event.rawPath,
      context: context.awsRequestId
    })
    
    return exceptionHandler.handle(error as Error)
  }
}

/**
 * Middleware para manejar errores de autenticación/autorización
 */
export class AuthErrorMiddleware implements ErrorMiddleware {
  handle(
    error: Error | ValidationError[],
    event: APIGatewayProxyEventV2,
    context: Context
  ): APIGatewayProxyResultV2 {
    // Aquí puedes agregar lógica específica para errores de autenticación
    // Por ejemplo, logging de intentos de acceso no autorizado
    console.warn('Authentication Error:', {
      error: (error as Error).message,
      event: event.rawPath,
      headers: event.headers,
      context: context.awsRequestId
    })
    
    return exceptionHandler.handle(error as Error)
  }
}

/**
 * Factory para crear middlewares de error específicos
 */
export class ErrorMiddlewareFactory {
  private static middlewares: Map<string, ErrorMiddleware> = new Map()

  static {
    // Registrar middlewares por defecto
    this.middlewares.set('ValidationError', new ValidationErrorMiddleware())
    this.middlewares.set('DatabaseError', new DatabaseErrorMiddleware())
    this.middlewares.set('AuthError', new AuthErrorMiddleware())
  }

  /**
   * Registra un nuevo middleware
   */
  static register(type: string, middleware: ErrorMiddleware): void {
    this.middlewares.set(type, middleware)
  }

  /**
   * Obtiene un middleware específico
   */
  static get(type: string): ErrorMiddleware | undefined {
    return this.middlewares.get(type)
  }

  /**
   * Obtiene todos los middlewares registrados
   */
  static getAll(): Map<string, ErrorMiddleware> {
    return new Map(this.middlewares)
  }
} 