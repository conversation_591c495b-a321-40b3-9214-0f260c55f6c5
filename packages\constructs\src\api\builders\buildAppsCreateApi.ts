import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsCreateApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
};


export const buildAppsCreateApi = (
    scope: Construct,
    props: AppsCreateApiProps,
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod'
    } = props;

    const createFunction = new lambda.Function(scope, 'AppsCreateFunction', {
        functionName: 'ah-create-apps-lambda',
        description: `POST API handler for apps registration in ${stage} environment`,
        code,
        handler: 'index.createApp',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('POST', new api.LambdaIntegration(createFunction), {
        authorizationType: api.AuthorizationType.NONE,
        operationName: 'createApp',
    });
};