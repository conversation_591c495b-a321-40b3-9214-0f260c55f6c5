import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsGetAllApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
};

/** Builder que añade GET /apps (todos los aplicativos) */
export const buildAppsGetApi = (
    scope: Construct,
    props: AppsGetAllApiProps
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod',
    } = props;

    const getAppsFunction = new lambda.Function(scope, 'AppsGetFunction', {
        functionName: 'ah-get-apps-lambda',
        description: `GET API for /apps in ${stage} environment` ,
        code,
        handler: 'index.getApps',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('GET',
        new api.LambdaIntegration(getAppsFunction),
        {
            authorizationType: api.AuthorizationType.NONE,
            operationName: 'getApps',
        }
    );
};
