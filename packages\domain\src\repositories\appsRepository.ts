import { prisma, PrismaTx } from "../prismaClient";
import { Prisma, Aplicativo } from "@prisma/client";

export class AppsRepository {

    constructor(private readonly prismaClient = prisma) { }

    async create(item: Prisma.AplicativoCreateInput): Promise<Aplicativo> {
        const app = await this.prismaClient.aplicativo.create({
            data: item
        });

        return app;
    }

    async createTx(tx: PrismaTx, item: Prisma.AplicativoCreateInput): Promise<Aplicativo> {
        const app = await tx.aplicativo.create({
            data: item
        });

        return app;
    }

    async update(id: number, item: Prisma.AplicativoUpdateInput): Promise<Aplicativo> {
        const app = await this.prismaClient.aplicativo.update({
            where: { id },
            data: item
        });
        return app;
    }

    async findById(id: number): Promise<Aplicativo | null> {
        const app = await this.prismaClient.aplicativo.findUnique({
            where: { id }
        });
        return app ? app : null;
    }

    async findByName(name: string): Promise<Aplicativo | null> {
        const app = await this.prismaClient.aplicativo.findFirst({
            where: { name }
        });
        return app ? app : null;
    }

    async findByKey(key: string): Promise<Aplicativo | null> {
        const app = await this.prismaClient.aplicativo.findFirst({
            where: { key }
        });
        return app ? app : null;
    }

    async findByNameAndNotDeleted(name: string): Promise<Aplicativo | null> {
        const app = await this.prismaClient.aplicativo.findFirst({
            where: {
                name: name,
                deleted: false
            }
        });
        return app ? app : null;
    }

    async findByKeyAndNotDeleted(key: string): Promise<Aplicativo | null> {
        const app = await this.prismaClient.aplicativo.findFirst({
            where: {
                key: key,
                deleted: false
            }
        });
        return app ? app : null;
    }

    async findAll(): Promise<Aplicativo[]> {
        const apps = await this.prismaClient.aplicativo.findMany({
            orderBy: { dateHourCreate: 'desc' }
        });
        return apps;
    }

    async findWithBasicFilters(filters: {
        deleted?: boolean;
        status?: string;
        page?: number;
        limit?: number;
    }): Promise<Aplicativo[]> {
        const { deleted, status, page, limit } = filters;

        const whereConditions: Prisma.AplicativoWhereInput = {};

        if (deleted === true) {
            whereConditions.deleted = true;
        } else if (deleted === false) {
            whereConditions.deleted = false;
        }

        if (status) {
            whereConditions.status = status;
        }

        const skip = page && limit ? (page - 1) * limit : undefined;
        const take = limit || undefined;

        const apps = await this.prismaClient.aplicativo.findMany({
            where: whereConditions,
            orderBy: { dateHourCreate: 'desc' },
            skip,
            take
        });

        return apps;
    }

    async countWithBasicFilters(filters: {
        deleted?: boolean;
        status?: string;
    }): Promise<number> {
        const { deleted, status } = filters;

        const whereConditions: Prisma.AplicativoWhereInput = {};

        if (deleted === true) {
            whereConditions.deleted = true;
        } else if (deleted === false) {
            whereConditions.deleted = false;
        }

        if (status) {
            whereConditions.status = status;
        }

        return await this.prismaClient.aplicativo.count({
            where: whereConditions
        });
    }

    async softDelete(id: number, deletedBy: string): Promise<Aplicativo> {
        const app = await this.prismaClient.aplicativo.update({
            where: { id },
            data: {
                deleted: true,
                deletedBy: deletedBy,
                dateHourUpdate: new Date(),
                updatedBy: deletedBy
            }
        });
        return app;
    }

}
