{"name": "@microsip/http-layer", "version": "0.0.1", "private": true, "description": "Microsip Online Billing Common Http Lambda Layer", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-users-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Lambda", "Layer", "Microsip", "Arquitectura hibrida"], "license": "ISC", "homepage": ".", "main": ".dist/http-layer/index.js", "exports": {".": "./src/index.ts", "./response": "./src/response/index.ts", "./exceptions": "./src/exceptions/index.ts", "./middleware": "./src/middleware/index.ts", "./enums": "./src/enums/index.ts", "./models": "./src/models/index.ts"}, "scripts": {"prebuild": "rimraf .dist", "build": "tsc", "test": "echo 'No tests my lord'", "test:ci": "echo 'No tests my lord'", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {"@types/aws-lambda": "^8.10.149", "@types/uuid": "^10.0.0"}, "dependencies": {"aws-lambda": "^1.0.7", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "dotenv": "^16.5.0", "find-up": "^7.0.0", "http-status-codes": "^2.3.0", "rimraf": "^6.0.1", "semver": "^7.7.2", "uuid": "^11.1.0"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}