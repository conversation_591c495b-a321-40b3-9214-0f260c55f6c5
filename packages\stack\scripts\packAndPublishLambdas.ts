import * as path from 'path'
import { promises as fs } from 'fs'
import { spawn } from 'child_process'
import { rimraf } from 'rimraf'
import { packBaseLayer, WebpackMode, packLambda } from '@microsip/lambda-package-utils'
import { PackAndPublishLambdasReturnProps } from '../lib/model'

export type PackAndPublishLambdasProps = {
  rootDir: string
  stackName: string
  stage: string
  sourceCodeBucketName: string
}

export const installNpmDependencies = (dir: string): Promise<void> => {
  return new Promise<void>((resolve, reject) => {
    const p = spawn('npm', ['i','--ignore-scripts', '--only=prod'], {
      cwd: dir,
      stdio: 'inherit',
    })
    p.on('close', code => {
      if (code !== 0) {
        const msg = `[CloudFormation Layer] npm i in ${dir} exited with code ${code}.`
        return reject(new Error(msg))
      }
      return resolve()
    })
    p.on('error', (err) => {
      console.error('Error al ejecutar npm:', err);
    })
  })
}

export const runPrismaGenerate = (dir: string): Promise<void> => {
  return new Promise<void>((resolve, reject) => {
    const p = spawn('npx', [
      'prisma',
      'generate',
    ], {
      stdio: 'inherit',
      env: {
        ...process.env,
        PRISMA_CLI_BINARY_TARGETS: 'rhel-openssl-3.0.x'
      },
      cwd: dir
    })
    p.on('close', code => {
      if (code !== 0) {
        const msg = `Prisma generate exited with code ${code}.`
        return reject(new Error(msg))
      }
      return resolve()
    })
    p.on('error', (err) => {
      console.error('Error al ejecutar prisma generate:', err);
      reject(err)
    })
  })
}

export const rm = async (file: string): Promise<void> => {
  await rimraf(file);
};

export const packAndPublishLambdas = async ({
  rootDir,
  stackName,
  sourceCodeBucketName,
}: // eslint-disable-next-line consistent-return
  PackAndPublishLambdasProps): Promise<PackAndPublishLambdasReturnProps> => {
  try {
    const outDir = path.resolve(rootDir, '../..', '.dist')

    try {
      await fs.stat(outDir)
    } catch (_) {
      await fs.mkdir(outDir)
    }



    const commonLayerFolder = path.resolve(rootDir, '..', 'common-layer')

    await installNpmDependencies(commonLayerFolder)



    await runPrismaGenerate(commonLayerFolder)

    const commonLayerFolderZipFileName = await packBaseLayer({
      srcDir: commonLayerFolder,
      outDir,
      Bucket: sourceCodeBucketName,
      layerName: `${stackName}-common-layer`,
      onlyCopyNodeModules: true,

    })

    await rm(path.resolve(commonLayerFolder, 'package-lock.json'))


    const lambdaPackages = [
      'ah-create-apps-lambda',
      'ah-update-apps-lambda',
      'ah-get-app-by-id-lambda',
      'ah-get-app-by-key-lambda',
      'ah-get-apps-lambda',
      'ah-soft-delete-app-lambda'
    ]

    const lambdaPromises: Array<
      Promise<{
        name: string
        zipFileName: string
        dependencies: {
          files: string[]
          checksum: string
          hashes: { [key: string]: string }
        }
      }>
    > = lambdaPackages.map(async (lambdaPackage: string) => {
      const lambdaFolder = path.resolve(rootDir, '..', lambdaPackage)

      return packLambda({
        mode: WebpackMode.production,
        name: `${stackName}-${lambdaPackage}`,
        src: path.resolve(lambdaFolder, 'src', 'index.ts'),
        srcDir: lambdaFolder,
        outDir,
        Bucket: sourceCodeBucketName,
        tsConfig: path.resolve(lambdaFolder, 'tsconfig.json'),
      })
    })

    const lambdasAwait = await Promise.all(lambdaPromises)

    const lambdas: { [key: string]: string } = lambdasAwait.reduce(
      (obj: { [key: string]: string }, lambdaAwait) => {
        return { ...obj, [lambdaAwait.name]: lambdaAwait.zipFileName }
      },
      {},
    )

    return {
      sourceCodeBucketName,
      commonLayerFolderZipFileName,
      lambdas,
    }
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error(err)
    process.exit(1)
  }
}
