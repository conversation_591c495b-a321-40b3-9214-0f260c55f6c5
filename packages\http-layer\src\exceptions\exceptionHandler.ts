import { APIGatewayProxyResultV2 } from 'aws-lambda'
import { StatusCodes, ReasonPhrases } from 'http-status-codes'
import { response } from '../response/responseUtils'
import { ApplicationException } from './applicationException'
import { ErrorApiResponse } from '../response/baseResponse'

/**
 * Interfaz para el manejador de excepciones
 */
export interface ExceptionHandler {
  handle(exception: Error): APIGatewayProxyResultV2
}

/**
 * Manejador de excepciones específico para ApplicationException
 */
export class ApplicationExceptionHandler implements ExceptionHandler {
  handle(exception: ApplicationException): APIGatewayProxyResultV2 {
    const body: ErrorApiResponse = {
      success: false,
      error: {
        code: exception.statusCode,
        message: exception.message,
        status: exception.status,
        details: exception.details || [],
      }
    }

    return response({
      statusCode: exception.statusCode,
      body,
    })
  }
}

/**
 * Manejador de excepciones para errores genéricos
 */
export class GenericExceptionHandler implements ExceptionHandler {
  handle(exception: Error): APIGatewayProxyResultV2 {
    const body: ErrorApiResponse = {
      success: false,
      error: {
        code: StatusCodes.INTERNAL_SERVER_ERROR,
        message: exception.message || ReasonPhrases.INTERNAL_SERVER_ERROR,
        status: 'INTERNAL_SERVER_ERROR',
        details: [],
      }
    }

    return response({
      statusCode: StatusCodes.INTERNAL_SERVER_ERROR,
      body,
    })
  }
}

/**
 * Manejador de excepciones principal que coordina los diferentes handlers
 */
export class ExceptionHandlerManager {
  private handlers: Map<string, ExceptionHandler> = new Map()

  constructor() {
    // Registrar handlers específicos
    this.handlers.set('ApplicationException', new ApplicationExceptionHandler())
    this.handlers.set('InvalidBodyException', new ApplicationExceptionHandler())
    this.handlers.set('BadRequestException', new ApplicationExceptionHandler())
    this.handlers.set('UnauthorizedException', new ApplicationExceptionHandler())
    this.handlers.set('NotFoundException', new ApplicationExceptionHandler())
    this.handlers.set('ConflictException', new ApplicationExceptionHandler())
    this.handlers.set('InternalServerException', new ApplicationExceptionHandler())
    this.handlers.set('ItemAlreadyExistsException', new ApplicationExceptionHandler())
    this.handlers.set('NoBodyProvidedException', new ApplicationExceptionHandler())
    this.handlers.set('default', new GenericExceptionHandler())
  }

  /**
   * Registra un nuevo handler para un tipo específico de excepción
   */
  registerHandler(exceptionType: string, handler: ExceptionHandler): void {
    this.handlers.set(exceptionType, handler)
  }

  /**
   * Maneja una excepción usando el handler apropiado
   */
  handle(exception: Error): APIGatewayProxyResultV2 {
    // Usar exceptionName si está disponible, sino usar constructor.name
    const exceptionType = (exception as any).exceptionName || exception.constructor.name
    
    const handler = this.handlers.get(exceptionType) || this.handlers.get('default')
    
    if (!handler) {
      throw new Error(`No handler found for exception type: ${exceptionType}`)
    }

    return handler.handle(exception)
  }
}

/**
 * Instancia singleton del manejador de excepciones
 */
export const exceptionHandler = new ExceptionHandlerManager() 