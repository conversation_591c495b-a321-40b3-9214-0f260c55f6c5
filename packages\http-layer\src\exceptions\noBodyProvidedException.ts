import { StatusCodes } from 'http-status-codes'
import { ApplicationException } from './applicationException'

export class NoBodyProvidedException extends ApplicationException {
  constructor(
    message: string = 'No se proporcionó cuerpo en la solicitud',
    details?: Array<{ field: string, message: string }>
  ) {
    super(message, details, StatusCodes.BAD_REQUEST, 'NoBodyProvidedException')
  }
}
