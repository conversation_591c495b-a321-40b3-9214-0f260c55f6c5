import * as path from 'path'
import { promises as fs } from 'fs'

import * as dotenv from 'dotenv'
import { findEnv } from '@microsip/util'
import { packAndPublishLambdas } from './packAndPublishLambdas'
import { PackAndPublishLambdasReturnProps } from '../lib/model'

dotenv.config({
  path: findEnv(process.env.DOTENV_CONFIG_PATH || '.env'),
})

const stackName = process.env.STACK_NAME || 'AHAppsMicroserviceStack'
const stage = process.env.STAGE || 'dev'
const sourceCodeBucketName = process.env.LAMBDAS_BUCKET_NAME || 'ah-apps-microservice-msp'


if (stackName === undefined) {
  // eslint-disable-next-line no-console
  console.error(`STACK_NAME not set!`)
  process.exit(1)
}

if (stage === undefined) {
  // eslint-disable-next-line no-console
  console.error(`STAGE not set!`)
  process.exit(1)
}

if (sourceCodeBucketName === undefined) {
  // eslint-disable-next-line no-console
  console.error(`LAMBDAS_BUCKET_NAME not set!`)
  process.exit(1)
}

packAndPublishLambdas({ rootDir: process.cwd(), stackName, stage, sourceCodeBucketName })
  .then((lambdasDeployment: PackAndPublishLambdasReturnProps) => {
    return lambdasDeployment
  })
  .then(async (lambdasDeployment: PackAndPublishLambdasReturnProps) => {
    await fs.writeFile(
      path.join(process.cwd(), '../../lambdas.deployment.json'),
      JSON.stringify(lambdasDeployment, null, 2),
      'utf-8',
    )
  })
