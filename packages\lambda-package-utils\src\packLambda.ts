import chalk from 'chalk'
import webpack from 'webpack'
import * as path from 'path'
import * as fs from 'fs'
import * as yazl from 'yazl'
import { glob } from 'glob'
import nodeExternals from 'webpack-node-externals'
import { TsconfigPathsPlugin } from 'tsconfig-paths-webpack-plugin'
import { existsOnS3 } from './existsOnS3'
import { publishToS3 } from './publishToS3'
import { checkSumOfResolvedFiles } from './checkSum'
import * as crypto from 'crypto'
import { ProgressReporter, ConsoleProgressReporter } from './reporter'

// Use glob directly (v10+ returns a promise)
const g = glob

export enum WebpackMode {
  development = 'development',
  production = 'production',
  none = 'none',
}

/**
 * Packs the lambda and all of its inter-project dependencies using webpack and uploads it to S3
 */
export const packLambda = async (args: {
  mode: WebpackMode
  srcDir: string
  outDir: string
  Bucket: string
  name: string
  src: string
  tsConfig: string
  reporter?: ProgressReporter
  ignoreFolders?: string[]
}): Promise<{
  name: string
  zipFileName: string
  dependencies: {
    files: string[]
    checksum: string
    hashes: { [key: string]: string }
  }
}> => {
  const { tsConfig, mode, outDir, Bucket, name, src, reporter, srcDir } = args
  const r = reporter ?? ConsoleProgressReporter(name)
  const progress = r?.progress?.(name)
  const success = r?.success?.(name)
  const failure = r?.failure?.(name)
  const sizeInBytes = r?.sizeInBytes?.(name)

  try {
    fs.statSync(src)
  } catch (e) {
    failure?.(`The source file ${chalk.cyan(src)} for ${chalk.green(name)} does not exist!`)
    throw e
  }
  
  // TEMPORARY: Fix hash calculation - use absolute path with fs
  const path = require('path')
  const fs = require('fs')

  // Get all .ts files in src directory
  const srcPath = path.join(srcDir, 'src')
  console.log(`DEBUG ${name}: Looking in directory:`, srcPath)

  let filesArray: string[] = []
  try {
    if (fs.existsSync(srcPath)) {
      const files = fs.readdirSync(srcPath)
      filesArray = files
        .filter((file: string) => file.endsWith('.ts'))
        .map((file: string) => path.join(srcPath, file))
      console.log(`DEBUG ${name}: Found .ts files:`, filesArray)
    } else {
      console.log(`DEBUG ${name}: Directory does not exist:`, srcPath)
    }
  } catch (error) {
    console.log(`DEBUG ${name}: Error reading directory:`, error)
  }
  // Create a direct checksum from the resolved files
  const fileChecksums = await checkSumOfResolvedFiles(filesArray)
  const hashGenerator = crypto.createHash('sha1')
  hashGenerator.update(
    [...Object.entries(fileChecksums)]
      .map(([, _hash]) => _hash)
      .reduce((allHashes, _hash) => `${allHashes}${_hash}`, ''),
  )
  // TEMPORARY: Force unique hash to bypass cache
  const uniqueHash = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  const deps = {
    checksum: uniqueHash,
    hashes: fileChecksums,
    files: filesArray
  }
  const { checksum: hash, hashes } = deps
  const jsFilenameWithHash = `${name}-${hash}.js`
  const zipFilenameWithHash = `${name}-${hash}-layered.zip`
  const localPath = path.resolve(outDir, zipFilenameWithHash)

  fs.writeFileSync(
    path.resolve(outDir, `${name}-${hash}.hashes.json`),
    JSON.stringify(
      {
        hashes,
      },
      null,
      2,
    ),
    'utf-8',
  )

  // Check if it already has been built and published
  progress?.('Checking if lambda exists on S3')
  // TEMPORARY: Force rebuild - commented out S3 check
  // let fileSize = await existsOnS3(Bucket, zipFilenameWithHash, outDir)
  let fileSize = 0; // Force rebuild
  if (fileSize) {
    success?.('OK')
    sizeInBytes?.(fileSize)
    return {
      name,
      zipFileName: zipFilenameWithHash,
      dependencies: deps,
    }
  }

  // Check if it already has been built locally
  try {
    const { size } = fs.statSync(localPath)
    success?.('OK')
    sizeInBytes?.(size)
    // File exists
    progress?.('Publishing to S3', `-> ${Bucket}`)
    await publishToS3(Bucket, zipFilenameWithHash, localPath)
    await existsOnS3(Bucket, zipFilenameWithHash, outDir)
    return {
      name,
      zipFileName: zipFilenameWithHash,
      dependencies: deps,
    }
  } catch {
    // Pass
  }

  progress?.('Packing')
  await new Promise<void>((resolve, reject) =>
    // eslint-disable-next-line no-promise-executor-return
    webpack(
      {
        entry: [src],
        mode,
        target: 'node',
        externals: [nodeExternals()], // ignore all modules in node_modules folder
        module: {
          rules: [
            {
              test: /\.ts$/,
              loader: 'ts-loader',
              exclude: /node_modules/,
              options: {
                configFile: tsConfig,
                transpileOnly: true,
                experimentalWatchApi: true,
              },
            },
          ],
        },
        optimization: {
          removeAvailableModules: false,
          removeEmptyChunks: false,
          splitChunks: false,
        },
        resolve: {
          extensions: ['.ts', '.js'],
          plugins: [
            new TsconfigPathsPlugin({
              configFile: tsConfig,
              logLevel: 'INFO',
              extensions: ['.ts', '.js'],
            }),
          ],
        },
        output: {
          path: outDir,
          libraryTarget: 'umd',
          filename: jsFilenameWithHash,
        },
      },
      (err, stats) => {
        if ((err !== null && err !== undefined) || stats?.hasErrors()) {
          failure?.('webpack failed', err?.message as string)
          // eslint-disable-next-line no-console
          console.error(err)
          if (stats?.hasErrors() ?? false) {
            // eslint-disable-next-line no-console
            console.info(stats?.toString())
            // eslint-disable-next-line no-console
            console.info(stats?.toJson())
          }
          reject(err)
        }
        const f = path.resolve(outDir, jsFilenameWithHash)

        progress?.('Creating archive Paco')
        const zipfile = new yazl.ZipFile()
        zipfile.addFile(f, 'index.js')
        zipfile.addBuffer(Buffer.from(JSON.stringify(hashes, null, 2)), 'hashes.json')
        zipfile.outputStream.pipe(fs.createWriteStream(localPath)).on('close', () => {
          success?.('Lambda packed', `${Math.round(fs.statSync(localPath).size / 1024)}KB`)
          resolve()
        })
        zipfile.end()
      },
    ),
  )

  progress?.('Publishing to S3', `-> ${Bucket}`)
  await publishToS3(Bucket, zipFilenameWithHash, localPath)
  fileSize = await existsOnS3(Bucket, zipFilenameWithHash, outDir)
  sizeInBytes?.(fileSize)
  success?.('All done')

  return {
    zipFileName: zipFilenameWithHash,
    name,
    dependencies: deps,
  }
}
