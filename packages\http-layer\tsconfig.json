{"extends": "../../tsconfig.json", "compilerOptions": {"baseUrl": ".", "outDir": "dist", "rootDir": "src", "strict": true, "declaration": true, "skipLibCheck": true, "noUnusedParameters": true, "noUnusedLocals": true, "noImplicitThis": true, "noImplicitReturns": true, "removeComments": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "composite": true}, "include": ["src/**/*"], "exclude": [".dist", "jest.config.ts"]}