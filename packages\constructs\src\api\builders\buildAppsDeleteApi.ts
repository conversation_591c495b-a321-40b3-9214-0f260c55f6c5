import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsDeleteApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
    idParamName?: string;
};

/** Builder que añade DELETE /apps/{id} */
export const buildAppsDeleteApi = (
    scope: Construct,
    props: AppsDeleteApiProps,
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod',
        idParamName = 'id',
    } = props;

    const deleteFunction = new lambda.Function(scope, 'AppsDeleteFunction', {
        functionName: 'ah-soft-delete-app-lambda',
        description: `DELETE API handler for apps/{${idParamName}} in ${stage} environment`,
        code,
        handler: 'index.deleteApp',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('DELETE', new api.LambdaIntegration(deleteFunction), {
        authorizationType: api.AuthorizationType.NONE,
        operationName: 'deleteApp',
    });
};
