import { APIGatewayProxyResultV2, APIGatewayProxyStructuredResultV2, APIGatewayProxyResult } from 'aws-lambda'
import { Item } from '../models'
import { HTTPMethod, CORSHeaders } from '../enums'

// Función para generar la cadena de métodos HTTP permitidos desde el enum
const getAllowedMethods = (): string => {
  return Object.values(HTTPMethod).join(',')
}

// Función para generar la cadena de headers CORS permitidos desde el enum
const getAllowedHeaders = (): string => {
  return Object.values(CORSHeaders).join(', ')
}

// Headers por defecto para todas las respuestas
const DEFAULT_CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Access-Control-Allow-Methods': getAllowedMethods(),
  'Access-Control-Allow-Headers': getAllowedHeaders(),
}

interface EnhancedAPIGatewayProxyResultV2<T> extends APIGatewayProxyStructuredResultV2 {
  body?: string | T | Record<any, string> | any
}

interface EnhancedAPIGatewayProxyResult<T> extends APIGatewayProxyResult {
  body: string | T | Record<any, string> | any
}

/**
 * Función base para crear respuestas de API Gateway V2
 * Maneja la serialización del body y los headers por defecto
 */
export const response = <T extends Item>({
  statusCode,
  body,
  headers,
}: EnhancedAPIGatewayProxyResultV2<T>): APIGatewayProxyResultV2 => {
  console.log({ body })
  const finalHeaders = Object.assign(DEFAULT_CORS_HEADERS, headers)
  console.log({ finalHeaders })
  return {
    statusCode,
    headers: finalHeaders,
    body:
      typeof body === 'undefined'
        ? undefined
        : typeof body === 'string'
          ? body
          : JSON.stringify(body),
  }
}

/**
 * Función base para crear respuestas de API Gateway V1
 * Maneja la serialización del body y los headers por defecto
 */
export const responseV1 = <T extends Item>({
  statusCode,
  body,
  headers,
}: EnhancedAPIGatewayProxyResult<T>): APIGatewayProxyResult => {
  console.log({ body })
  return {
    statusCode,
    headers: Object.assign(DEFAULT_CORS_HEADERS, headers),
    body: typeof body === 'string' ? body : JSON.stringify(body),
  }
} 