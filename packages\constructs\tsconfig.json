{"extends": "../../tsconfig.json", "compilerOptions": {"baseUrl": ".", "outDir": "./dist", "rootDir": "./src", "paths": {"@microsip/util": ["../util/src"], "@microsip/ah-create-apps-lambda": ["../ah-create-apps-lambda/src"], "@microsip/ah-get-apps-lambda": ["../ah-get-apps-lambda/src"], "@microsip/ah-get-app-by-key-lambda": ["../ah-get-app-by-key-lambda/src"], "@microsip/ah-update-apps-lambda": ["../ah-update-apps-lambda/src"], "@microsip/ah-get-app-by-id-lambda": ["../ah-get-app-by-id-lambda/src"], "@microsip/ah-soft-delete-app-lambda": ["../ah-soft-delete-app-lambda/src"], "@microsip/lambda-package-utils": ["../lambda-package-utils/src"]}}, "include": ["src/**/*"], "exclude": ["dist", "jest.config.ts", "scripts"]}