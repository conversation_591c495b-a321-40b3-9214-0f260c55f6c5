import * as cdk from 'aws-cdk-lib'
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs'
import { RestApiConstruct, PrismaConfig } from '../api';
import { PackAndPublishLambdasReturnProps } from '../model';

interface RestApiNestedProps extends cdk.NestedStackProps {
  restApiId: string;
  restApiName: string;
  rootResourceId: string;
  stackName: string;
  lambdasDeployment: PackAndPublishLambdasReturnProps;
  sourceCodeBucket: s3.IBucket;
  configPrisma: PrismaConfig;
}

export class RestApiStack extends cdk.NestedStack {
  restApiConstruct: RestApiConstruct

  constructor(scope: Construct, id: string, { restApiId, restApiName, rootResourceId, stackName, lambdasDeployment, sourceCodeBucket, configPrisma }: RestApiNestedProps) {
    super(scope, id)



    new RestApiConstruct(this, 'RestApiConstruct', {
      restApiId: restApiId,
      restApiName: restApiName,
      rootResourceId: rootResourceId,
      lambdasDeployment: lambdasDeployment,
      sourceCodeBucket: sourceCodeBucket,
      stackName: stackName,
      configPrisma: configPrisma,
    });
  }
}