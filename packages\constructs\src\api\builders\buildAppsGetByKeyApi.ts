import * as api from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { Construct } from 'constructs';
import * as cdk from 'aws-cdk-lib';

export type AppsGetByKeyApiProps = {
    rootResource: api.IResource;
    code: lambda.Code;
    nodeModulesLayer: lambda.LayerVersion;
    tag: (strings: TemplateStringsArray, ...placeholders: string[]) => string;
    environment?: { [key: string]: string };
    stage?: string;
    keyParamName?: string;
};

/** Builder que añade GET /apps/key/{key} */
export const buildAppsGetByKeyApi = (
    scope: Construct,
    props: AppsGetByKeyApiProps,
): void => {
    const {
        rootResource,
        code,
        nodeModulesLayer,
        tag,
        environment,
        stage = 'prod',
        keyParamName = 'key',
    } = props;

    const getFunction = new lambda.Function(scope, 'AppsGetByKeyFunction', {
        functionName: 'ah-get-app-by-key-lambda',
        description: `GET API handler for apps/key/{${keyParamName}} in ${stage} environment`,
        code,
        handler: 'index.getAppByKey',
        runtime: lambda.Runtime.NODEJS_20_X,
        layers: [nodeModulesLayer],
        timeout: cdk.Duration.seconds(29),
        ...(environment && { environment }),
    });

    rootResource.addMethod('GET', new api.LambdaIntegration(getFunction), {
        authorizationType: api.AuthorizationType.NONE,
        operationName: 'getAppByKey',
    });
};
