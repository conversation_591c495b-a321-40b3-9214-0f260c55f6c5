import { CreateAppRequest } from '@microsip/models';
import { createAppImpl } from './api';
import { handleAsyncV2, createItemNoItem, ApiResponseManager } from '@microsip/http-layer';

export const createApp = handleAsyncV2(async (event) => {
    const item = await createItemNoItem(CreateAppRequest, event);

    const username = event.headers['X-USERNAME'] || event.headers['x-username'];
    if (username) {
        item.createdBy = username;
    }

    const savedItem = await createAppImpl(item);
    return ApiResponseManager.created(savedItem, 'Aplicativo creado exitosamente');
});

