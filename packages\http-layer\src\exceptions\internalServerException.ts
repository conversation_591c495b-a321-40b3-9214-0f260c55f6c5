import { StatusCodes } from 'http-status-codes'
import {ApplicationException} from './applicationException'

export class InternalServerException extends ApplicationException {
  constructor(
    message: string = 'Error interno del servidor',
    details?: Array<{ field: string, message: string }>
  ) {
    super(
      message,
      details,
      StatusCodes.INTERNAL_SERVER_ERROR,
      'InternalServerException'
    )
  }
}
