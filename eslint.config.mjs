import { defineConfig, globalIgnores } from "eslint/config";
import typescriptEslint from "@typescript-eslint/eslint-plugin";
import globals from "globals";
import tsParser from "@typescript-eslint/parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import eslintPluginImport from "eslint-plugin-import";


const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig([
  globalIgnores([
    "**/eslint.config.mjs",
    "**/yarn.lock",
    "**/node_modules/*",
    "**/out/*",
    "**/dist/*",
    "**/cdk.out/*",
    "**/*.js",
    "**/*.td.js",
    "**/*.d.ts"
  ]),

  js.configs.recommended,

  {
    plugins: {
      "@typescript-eslint": typescriptEslint,
      "import": eslintPluginImport,
    },

    languageOptions: {
      globals: {
        ...globals.node,
        ...globals.jest
      },
      parser: tsParser,
      ecmaVersion: 2020,
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: { legacyDecorators: true, modules: true },
        project: "tsconfig.eslint.json"
      }
    },

    settings: {
      "import/resolver": {
        "eslint-import-resolver-lerna": { packages: "./packages" },
        typescript: {},
        node: { extensions: [".js"] }
      }
    },

    rules: {
      // Buenas prácticas de TypeScript sin reglas problemáticas
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-useless-constructor": "off",
      "@typescript-eslint/no-unused-vars": ["warn", {
        vars: "all",
        args: "after-used",
        varsIgnorePattern: "[iI]gnored",
        argsIgnorePattern: "^_",
        ignoreRestSiblings: true
      }],

      // Reglas de estilo relajadas
      "import/prefer-default-export": "off",
      "class-methods-use-this": "off",
      "max-classes-per-file": "off",
      "no-plusplus": "off",
      "no-new": "off",
      "import/no-extraneous-dependencies": "off"
    }
  }
]);
