import { UpdateAppRequest, AppResponse, updateAppPrismaInput, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';
import { NotFoundException, ConflictException, BadRequestException } from '@microsip/http-layer';

const appsRepository = new AppsRepository();

export async function updateApp(id: number, request: UpdateAppRequest): Promise<AppResponse> {
    const existingApp = await appsRepository.findById(id);

    if (!existingApp) {
        throw new NotFoundException(
            `No existe un aplicativo con el ID ${id}`,
            [{ field: 'id', message: `No se encontró un aplicativo con el ID: ${id}` }]
        );
    }

    if (existingApp.deleted) {
        throw new BadRequestException(
            'El aplicativo ya está eliminado',
            [{ field: 'id', message: `El aplicativo con ID ${id} ya está marcado como eliminado` }]
        );
    }

    if (request.name && request.name !== existingApp.name) {
        const appWithSameName = await appsRepository.findByNameAndNotDeleted(request.name);
        if (appWithSameName && appWithSameName.id !== id) {
            throw new ConflictException(
                'El nombre ya pertenece a otro aplicativo',
                [{ field: 'name', message: `Ya existe un aplicativo con el nombre: ${request.name}` }]
            );
        }
    }

    const prismaInput = updateAppPrismaInput(request);
    const updatedApp = await appsRepository.update(id, prismaInput);

    return createAppResponse(updatedApp);
}
