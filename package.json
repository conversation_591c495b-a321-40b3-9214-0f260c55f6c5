{"name": "ah-apps-microservice", "version": "0.1.0", "private": true, "bin": {"ah-apps-microservice": "bin/ah-apps-microservice.js"}, "workspaces": ["packages/*"], "scripts": {"type-check": "tsc --pretty --noEmit", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "format": "prettier --write .", "prebuild": "rimraf .dist", "build": "lerna run --stream build", "cdk": "cdk", "deploy": "run-s cdk:deploy:lambdas cdk:deploy", "deploy:ci": "run-s cdk:deploy:lambdas cdk:deploy:ci", "cdk:deploy": "cdk deploy", "cdk:deploy:lambdas": "yarn workspace @microsip/stack pack-and-publish-lambdas", "cdk:deploy:ci": "cdk deploy --require-approval never --progress events", "cdk:build:deploy:lambdas": "run-s build cdk:deploy:lambdas"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.26.0", "@eslint/migrate-config": "^1.5.0", "@types/jest": "^29.5.14", "@types/node": "22.7.9", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "dotenv": "^16.5.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.3", "eslint-import-resolver-lerna": "^2.0.0", "eslint-import-resolver-typescript": "^4.3.4", "eslint-plugin-import": "^2.31.0", "eslint-plugin-json": "^4.0.1", "globals": "^16.1.0", "husky": "^9.1.7", "jest": "^29.7.0", "lerna": "^8.2.2", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "pretty-quick": "^4.1.1", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@prisma/client": "6.10.1"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}