import { glob } from 'glob'
import * as crypto from 'crypto'
import * as fs from 'fs'

// Use glob directly (v10+ returns a promise)
const g = glob

// TEMPORARY: Clear cache to force new hashes
const hashCache: { [key: string]: string } = {}

/**
 * Computes the sha1 checksum for the given files pattern (which allows wildcards).
 */
export const checkSum = async (filesOrPatterns: string[]): Promise<{ [key: string]: string }> => {
  // Get all files from patterns
  const allFiles: string[] = []
  for (const pattern of filesOrPatterns) {
    try {
      const matchedFiles = await g(pattern) as string[] | string
      // Handle different return types from glob
      if (Array.isArray(matchedFiles)) {
        allFiles.push(...matchedFiles)
      } else if (typeof matchedFiles === 'string') {
        allFiles.push(matchedFiles)
      }
    } catch (error) {
      console.warn(`Error processing pattern ${pattern}:`, error)
    }
  }

  const hashes: { [key: string]: string } = {}
  await allFiles
    .filter(file => file && file.length > 0) // Filter empty file matches
    .reduce(
      async (p, file) =>
        p.then(async () =>
          new Promise<string>(resolve => {
            // TEMPORARY: Disable cache to force new hashes
            // if (hashCache[`${file}`]) {
            //   resolve(hashCache[`${file}`])
            // }
            const hash = crypto.createHash('sha1')
            hash.setEncoding('hex')
            const fileStream = fs.createReadStream(`${file}`)
            fileStream.pipe(hash, { end: false })
            fileStream.on('end', () => {
              hash.end()
              const h = hash.read().toString()
              hashCache[`${file}`] = h
              resolve(h)
            })
          }).then(fileHash => {
            hashes[`${file}`] = fileHash
          }),
        ),
      Promise.resolve() as Promise<any>,
    )
  return hashes
}

/**
 * Computes the sha1 checksum for the given files (already resolved, not patterns).
 */
export const checkSumOfResolvedFiles = async (files: string[]): Promise<{ [key: string]: string }> => {
  const hashes: { [key: string]: string } = {}

  // Ensure files is an array
  const filesArray = Array.isArray(files) ? files : []

  await filesArray
    .filter(file => file && file.length > 0) // Filter empty file matches
    .reduce(
      async (p, file) =>
        p.then(async () =>
          new Promise<string>(resolve => {
            // TEMPORARY: Disable cache to force new hashes
            // if (hashCache[`${file}`]) {
            //   resolve(hashCache[`${file}`])
            // }
            const hash = crypto.createHash('sha1')
            hash.setEncoding('hex')
            const fileStream = fs.createReadStream(`${file}`)
            fileStream.pipe(hash, { end: false })
            fileStream.on('end', () => {
              hash.end()
              const h = hash.read().toString()
              hashCache[`${file}`] = h
              resolve(h)
            })
          }).then(fileHash => {
            hashes[`${file}`] = fileHash
          }),
        ),
      Promise.resolve() as Promise<any>,
    )
  return hashes
}

/**
 * Computes the combined checksum of the given files
 */
export const checkSumOfFiles = async (
  filesOrPatterns: string[],
): Promise<{
  checksum: string
  hashes: { [key: string]: string }
  files: string[]
}> => {
  const fileChecksums = await checkSum(filesOrPatterns)

  // Calculate hash of hashes
  const hash = crypto.createHash('sha1')
  hash.update(
    [...Object.entries(fileChecksums)]
      .map(([, _hash]) => _hash)
      .reduce((allHashes, _hash) => `${allHashes}${_hash}`, ''),
  )
  return {
    checksum: hash.digest('hex'),
    hashes: {
      ...fileChecksums,
    },
    files: Object.keys(fileChecksums),
  }
}
