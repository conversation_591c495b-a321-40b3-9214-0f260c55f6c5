import { APIGatewayProxyEventV2, Context } from 'aws-lambda'
import { handleAsyncV2, createItem } from '../src/apiGateway'
import { ApiResponseManager } from '../src/response/apiResponseManager'
import { 
  UnauthorizedException, 
  NotFoundException, 
  ConflictException,
  InternalServerException,
  NoBodyProvidedException 
} from '../src/exceptions'
import { IsEmail, IsNotEmpty, IsPhoneNumber, IsString, Matches, MinLength, MaxLength } from "class-validator"
import { Item } from '../src/models/item'

// Versión local del RegisterUserAdminRequest para el ejemplo
export class RegisterUserAdminRequest extends Item {
  constructor() {
    super();
  }

  @IsString({ message: "El campo debe ser un string" })
  @IsNotEmpty({ message: "El campo es requerido" })
  name: string;

  @IsEmail({}, { message: "El email no es válido" })
  @IsNotEmpty({ message: "El campo es requerido" })
  email: string;

  @IsNotEmpty({ message: "El campo es requerido" })
  @IsPhoneNumber('MX', { message: "El telefono debe contener 10 dígitos sin contar la clave +52" })
  @Matches(/^\+52\d{10}$/, { message: "El telefono debe contener la clave +52" })
  phone: string;

  @IsString({ message: "El campo debe ser un string" })
  @IsNotEmpty({ message: "El campo es requerido" })
  @Matches(/^[A-Z]+-[A-Z]{1,5}$/, {
    message: "La clave debe tener el formato: 3 letras MAYÚSCULAS, seguidas de un guión (-), seguidas de 1 a 5 letras MAYÚSCULAS. Ejemplo: ABC-DEFGH"
  })
  appId: string;

  @IsString({ message: "El campo debe ser un string" })
  @IsNotEmpty({ message: "El campo es requerido" })
  @MinLength(8, { message: "La contraseña debe tener al menos 8 caracteres" })
  @MaxLength(16, { message: "La contraseña debe tener menos de 16 caracteres" })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^A-Za-z\d])[A-Za-z\d\S]{8,}$/, {
    message: "La contraseña debe contener al menos una letra mayúscula, una minúscula, un número y un carácter especial"
  })
  password: string;
}

/**
 * Ejemplo de uso del middleware de errores integrado
 * 
 * Este ejemplo muestra cómo el middleware maneja automáticamente:
 * 1. Errores de validación (ValidationError[]) - Código 400
 * 2. Errores de autenticación/autorización - Código 401
 * 3. Errores de recursos no encontrados - Código 404
 * 4. Errores de conflicto - Código 409
 * 5. Errores de servidor interno - Código 500
 * 6. Otros errores genéricos
 */

// Ejemplo 1: Función que puede generar errores de validación
export const exampleUserRegistration = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Esta función usa createItem internamente, que ahora lanza ValidationError[] directamente
  // El middleware se encarga automáticamente de convertirlos a InvalidBodyException con código 400
  const userRequest = await createItem(RegisterUserAdminRequest, context, event)
  
  // Simular guardado exitoso
  return ApiResponseManager.created(userRequest)
})

// Ejemplo 2: Función que puede generar errores de autenticación
export const exampleAuthOperation = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Simular un error de autenticación
  if (!event.headers.authorization) {
    throw new UnauthorizedException('Token de autorización requerido')
  }
  
  return ApiResponseManager.success({ message: 'Authentication successful' })
})

// Ejemplo 3: Función que puede generar errores de recurso no encontrado
export const exampleResourceNotFound = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  const resourceId = event.pathParameters?.id
  
  // Simular recurso no encontrado
  if (!resourceId || resourceId === 'not-found') {
    throw new NotFoundException('Usuario no encontrado')
  }
  
  return ApiResponseManager.success({ id: resourceId, message: 'Resource found' })
})

// Ejemplo 4: Función que puede generar errores de conflicto
export const exampleConflictOperation = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Simular conflicto (ej: email ya existe)
  if (event.body?.includes('duplicate')) {
    throw new ConflictException('El email ya está registrado')
  }
  
  return ApiResponseManager.success({ message: 'Operation completed successfully' })
})

// Ejemplo 5: Función que puede generar errores de servidor interno
export const exampleInternalServerError = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Simular error interno del servidor
  if (event.body?.includes('server-error')) {
    throw new InternalServerException('Error en la base de datos')
  }
  
  return ApiResponseManager.success({ message: 'Operation successful' })
})

// Ejemplo 6: Función que puede generar errores de cuerpo no proporcionado
export const exampleNoBodyProvided = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Simular error de cuerpo no proporcionado
  if (!event.body) {
    throw new NoBodyProvidedException()
  }
  
  return ApiResponseManager.success({ message: 'Body received successfully' })
})

// Ejemplo 7: Función que puede generar errores de base de datos (genéricos)
export const exampleDatabaseOperation = handleAsyncV2(async (event: APIGatewayProxyEventV2, context: Context) => {
  // Simular un error de base de datos genérico
  if (event.body?.includes('db-error')) {
    throw new Error('Database connection failed')
  }
  
  return ApiResponseManager.success({ message: 'Database operation successful' })
})

/**
 * Beneficios de esta implementación:
 * 
 * 1. **Código más limpio**: No más bloques de validación repetitivos
 * 2. **Centralización**: Todo el manejo de errores está en un lugar
 * 3. **Logging mejorado**: Los middlewares pueden agregar logging específico con contexto
 * 4. **Extensibilidad**: Fácil agregar nuevos tipos de middleware
 * 5. **Consistencia**: Todos los errores se manejan de la misma manera
 * 6. **Códigos de estado correctos**: Cada tipo de error tiene su código HTTP apropiado
 * 
 * El flujo ahora es:
 * 1. Las funciones lanzan errores (ValidationError[], excepciones específicas, Error genérico)
 * 2. handleAsyncV2 los captura
 * 3. El middleware apropiado los procesa basado en el tipo de excepción
 * 4. Se genera una respuesta consistente con el código de estado correcto
 * 
 * Respuestas de ejemplo:
 * 
 * Error de validación (400):
 * {
 *   "success": false,
 *   "error": {
 *     "code": 400,
 *     "message": "Errores de validación",
 *     "status": "Bad Request",
 *     "details": [
 *       { "field": "email", "message": "Invalid email format" }
 *     ]
 *   }
 * }
 * 
 * Error de autenticación (401):
 * {
 *   "success": false,
 *   "error": {
 *     "code": 401,
 *     "message": "Token de autorización requerido",
 *     "status": "Unauthorized",
 *     "details": []
 *   }
 * }
 */ 