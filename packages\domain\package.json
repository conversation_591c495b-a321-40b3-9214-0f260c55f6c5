{"name": "@microsip/domain", "version": "0.0.1", "private": true, "description": "Domain for the microsip project", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Lambda", "Layer", "Microsip", "Arquitectura hibrida"], "license": "ISC", "homepage": ".", "types": ".dist/index.d.ts", "scripts": {"prebuild": "rimraf .dist", "build": "tsc", "test": "echo 'No tests my lord'", "test:ci": "echo 'No tests my lord'", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {}, "dependencies": {"@microsip/models": "^0.0.1", "@prisma/client": "^6.10.1"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}