#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import * as dotenv from 'dotenv'
import { AHAppsMicroserviceStack } from '../lib/ahAppsMicroserviceStack';
import { readLambdasDeployment } from '../lib/util';
import { findEnv } from '@microsip/util';

dotenv.config({
  path: findEnv(process.env.DOTENV_CONFIG_PATH || '.env'),
})

const tags = {
  stage: process.env.STAGE || 'dev',
  'appco.departamento': process.env.DEPARTMENT || 'desarrollo',
  proyecto: process.env.PROJECT || 'arquitectura hibrida'
}

readLambdasDeployment('lambdas.deployment.json')
  .then(lambdasDeployment => {
    console.log("lambdasDeployment:::", lambdasDeployment)
    const app = new cdk.App();
    new AHAppsMicroserviceStack(app, 'AHAppsMicroserviceStack',
      {
        description: `Microsip Arquitectura Hibrida Microservicio Aplicaciones Stack`,
        stackName: `AHAppsMicroserviceStack`,
        tags,
        terminationProtection: process.env.NODE_ENV === 'production',
        analyticsReporting: true,
        lambdasDeployment
      });
  })
  .catch(error => {
    console.error('Error al inicializar el stack:', error);
    process.exit(1);
  });