import { CreateAppRequest, AppResponse, createAppPrismaInput, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';
import { ConflictException } from '@microsip/http-layer';

const appsRepository = new AppsRepository();

export async function createApp(request: CreateAppRequest): Promise<AppResponse> {
    const existingAppByName = await appsRepository.findByNameAndNotDeleted(request.name);
    if (existingAppByName) {
        throw new ConflictException(
            'El nombre ya pertenece a otro aplicativo',
            [{ field: 'name', message: `Ya existe un aplicativo con el nombre: ${request.name}` }]
        );
    }

    const existingAppByKey = await appsRepository.findByKeyAndNotDeleted(request.key);
    if (existingAppByKey) {
        throw new ConflictException(
            'La clave ya pertenece a otro aplicativo',
            [{ field: 'key', message: `Ya existe un aplicativo con la clave: ${request.key}` }]
        );
    }

    const prismaInput = createAppPrismaInput(request);
    const createdApp = await appsRepository.create(prismaInput);

    const response = createAppResponse(createdApp);
    return response;
}
