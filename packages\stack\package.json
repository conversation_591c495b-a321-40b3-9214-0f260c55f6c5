{"name": "@microsip/stack", "version": "0.0.1", "private": true, "description": "Microsip Arquitectura Hibrida Apps Microservice", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "bin": {"ah-apps-microservice": "bin/ah-apps-microservice.js"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Microsip", "Arquitectura Hibrida"], "license": "ISC", "homepage": ".", "scripts": {"pack-and-publish-lambdas": "run-s clean build pack-and-publish", "pack-and-publish": "ts-node -r tsconfig-paths/register scripts/run.ts", "clean": "tsc --build --clean", "prebuild": "rimraf .dist", "build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "deploy": "cdk deploy", "destroy": "cdk destroy", "diff": "cdk diff", "synth": "cdk synth", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {"aws-cdk": "2.1013.0", "typescript": "~5.6.3"}, "dependencies": {"@microsip/lambda-package-utils": "^0.0.1", "@microsip/util": "^0.0.1", "aws-cdk-lib": "2.190.0", "constructs": "^10.0.0", "dotenv": "^16.5.0", "rimraf": "^6.0.1", "ts-jest": "^29.3.3"}}