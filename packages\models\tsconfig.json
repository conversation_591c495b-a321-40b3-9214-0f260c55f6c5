{
  "extends": "../../tsconfig.json",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@microsip/util": ["../util/src"]
    },
    "outDir": ".dist",
    //"rootDir": "src",
    "strict": true,
    "declaration": true,
    "skipLibCheck": true,
    "noUnusedParameters": true,
    "noUnusedLocals": true,
    "noImplicitThis": true,
    "noImplicitReturns": true,
    "removeComments": true,
    "noFallthroughCasesInSwitch": true,
    "forceConsistentCasingInFileNames": true,
    "noImplicitAny": true,
    "composite": true 
  },
  "include": ["src/**/*", "src/apps"],
  "exclude": [".dist", "jest.config.ts"]
}
