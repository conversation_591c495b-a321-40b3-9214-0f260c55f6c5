import {
    Is<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>
} from 'class-validator';
import { AppStatus } from '../enums';

export class CreateAppRequest {
    @IsString({ message: 'El nombre debe ser un string' })
    @IsNotEmpty({ message: 'El nombre es obligatorio.' })
    @MinLength(3, { message: 'El nombre debe tener al menos 3 caracteres' })
    @MaxLength(100, { message: 'El nombre debe tener máximo 100 caracteres' })
    name: string;

    @IsString({ message: 'La clave del aplicativo debe ser un texto válido' })
    @IsNotEmpty({ message: 'La clave del aplicativo es requerida' })
    @Matches(/^[A-Z]+-[A-Z]{1,5}$/,
        { message: 'La clave debe tener el formato: 3 letras MAYÚSCULAS, seguidas de un guión (-), seguidas de 1 a 5 letras MAYÚSCULAS. Ejemplo: ABC-DEFGH' }
    )
    key: string;

    @IsEnum(AppStatus, { message: 'El estatus debe ser A (Activo) o I (Inactivo)' })
    @IsOptional()
    status?: AppStatus;

    @IsString()
    @IsOptional()
    createdBy?: string;
}
