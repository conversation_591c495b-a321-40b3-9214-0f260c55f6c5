import { promises as fs } from 'fs'
import { findFile } from '@microsip/util'
import { PackAndPublishLambdasReturnProps } from '../model'

export enum TaggerStyleTypes {
  CAMEL_CASE,
  HYPHENS,
  DOTS,
  UNDERSCORE,
}

export const capitalize = (s: string): string => {
  return s.charAt(0).toUpperCase() + s.slice(1)
}

// @see https://basarat.gitbook.io/typescript/future-javascript/template-strings
export const replacePlaceholders = (
  literals: TemplateStringsArray,
  ...placeholders: Array<string>
): string => {
  let result = ''

  // interleave the literals with the placeholders
  for (let i = 0; i < placeholders.length; i++) {
    result += literals[i]
    result += placeholders[i]
  }

  // add the last literal
  result += literals[literals.length - 1]
  return result
}

/**
 *
 * @param id This function creates a template function for creating the name of the resources in a consistent format
 * @param style creates the tag with any of the options. If no option is given, no transformation will be made, just an append
 */
export const createTagger = (id: string, style?: TaggerStyleTypes) => (
  string: TemplateStringsArray,
  ...placeholders: Array<string>
): string => {
  switch (style) {
    case TaggerStyleTypes.CAMEL_CASE:
      return capitalize(
        `${id}${replacePlaceholders(string, ...placeholders)}`.replace(/-([a-z|A-Z])/g, val =>
          val[1].toUpperCase(),
        ),
      )
    case TaggerStyleTypes.HYPHENS:
      return `${`${id}-${replacePlaceholders(string, ...placeholders)}`
        .replace(/([a-z])([A-Z])/g, `$1-$2`)
        .toLowerCase()}`
    case TaggerStyleTypes.DOTS:
      return `${`${id}.${replacePlaceholders(string, ...placeholders)}`
        .replace(/([a-z])([A-Z])/g, '$1.$2')
        .toLowerCase()}`
    case TaggerStyleTypes.UNDERSCORE:
      return `${`${id}_${replacePlaceholders(string, ...placeholders)}`
        .replace(/([a-z])([A-Z])/g, '$1_$2')
        .toLowerCase()}`
    default:
      return `${id}${replacePlaceholders(string, ...placeholders)}`
  }
}

export const readLambdasDeployment = async (
  fileName: string,
): Promise<PackAndPublishLambdasReturnProps> => {
  const lambdasDeploymentFile = findFile(fileName)
  const lambdasDeploymentJSON = await fs.readFile(lambdasDeploymentFile as string, 'utf-8')
  const lambdasDeployment: PackAndPublishLambdasReturnProps = JSON.parse(
    lambdasDeploymentJSON as string,
  )
  return lambdasDeployment
}
