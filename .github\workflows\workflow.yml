name: Deploy stack arquitectura hibrida apps microservice microsip

on:
  push:
    branches:
      - 'main'
      - 'desarrollo'
      #- 'feature/**'
      - 'hotfix/**'
jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: checkout
        uses: actions/checkout@v3

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 20
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Install tools
        run: |
          npm install -g aws-cdk@2.1013.0
          npm install -g lerna@8.2.2
          npm install -g npm-run-all
      
      - name: set npm global as default
        run: |
          npm install -g npm@6
          npm config set prefix ~/.npm-global
          export PATH="$PATH:~/.npm-global/bin"
      - name: npm version
        run: npm --version

      #https://github.com/yarnpkg/yarn/issues/2739
      - name: Installing dependencies
        run: yarn install --production=false
      - name: Build project adn publis lambdas
        run: yarn cdk:build:deploy:lambdas
      - name: Boostrap aws
        run: cdk bootstrap aws://${{ secrets.CDK_DEPLOY_ACCOUNT }}/${{ secrets.AWS_REGION }}
      - name: Deploy project
        run: |
          yarn run cdk:deploy:ci
          echo "Finalizing deploy"
