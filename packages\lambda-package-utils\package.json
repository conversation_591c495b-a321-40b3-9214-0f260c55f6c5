{"name": "@microsip/lambda-package-utils", "version": "0.0.1", "private": true, "description": "Packages lambda functions and layers for AWS lambda", "engines": {"node": ">=16.20.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Lambda", "Layer", "Microsip", "Arquitectura hibrida"], "license": "ISC", "homepage": ".", "scripts": {"prebuild": "rimraf .dist", "build": "tsc", "test": "echo 'No tests my lord'", "test:ci": "echo 'No tests my lord'", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {"@types/aws-lambda": "8.10.71", "@types/dependency-tree": "8.0.0", "@types/fs-extra": "9.0.8", "@types/glob": "7.1.3", "@types/table": "6.0.0", "@types/terminal-kit": "1.28.2", "@types/tmp": "0.2.0", "@types/uuid": "8.3.0", "@types/webpack": "^5.28.1", "@types/webpack-node-externals": "^3.0.0", "@types/yazl": "2.4.2"}, "dependencies": {"@aws-sdk/client-cloudformation": "^3.540.0", "@aws-sdk/client-lambda": "^3.540.0", "@aws-sdk/client-s3": "^3.540.0", "ansi-escapes": "4.3.1", "aws-lambda": "1.0.6", "chalk": "4.1.0", "dependency-tree": "8.0.0", "fs-extra": "9.1.0", "glob": "7.1.6", "rimraf": "^3.0.2", "table": "6.0.7", "tmp": "0.2.1", "ts-loader": "^9.4.4", "tsconfig-paths-webpack-plugin": "4.1.0", "webpack": "^5.88.2", "webpack-node-externals": "^3.0.0", "yazl": "2.5.1", "semver": "^7.3.8"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}