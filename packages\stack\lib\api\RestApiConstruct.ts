import { Construct } from 'constructs';
import * as apigateway from 'aws-cdk-lib/aws-apigateway';
import * as lambda from 'aws-cdk-lib/aws-lambda';
import { buildAppsCreateApi, buildAppsUpdateApi, buildAppsGetByIdApi, buildAppsGetByKeyApi, buildAppsDeleteApi, buildAppsGetApi } from '@microsip/constructs';
import { PackAndPublishLambdasReturnProps } from '../model';
import * as s3 from 'aws-cdk-lib/aws-s3';

export type PrismaConfig = {
  DB_URL: string;
  LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY: string;
};

export type RestApiConstructProps = {
  restApiId: string;
  restApiName: string;
  rootResourceId: string;
  lambdasDeployment: PackAndPublishLambdasReturnProps;
  sourceCodeBucket: s3.IBucket;
  stackName: string;
  configPrisma: PrismaConfig;
};

export class RestApiConstruct extends Construct {
  public readonly api: apigateway.IRestApi;

  constructor(scope: Construct, id: string, { restApiId, rootResourceId, lambdasDeployment, sourceCodeBucket, stackName, configPrisma }: RestApiConstructProps) {
    super(scope, id);

    this.api = apigateway.RestApi.fromRestApiAttributes(this, 'ImportedRestApi', {
      restApiId: restApiId,
      rootResourceId: rootResourceId,
    });

    const appsResource = this.api.root.addResource('apps');

    let v1Resource: apigateway.IResource | undefined = appsResource.getResource('v1');
    if (!v1Resource) {
      v1Resource = appsResource.addResource('v1');
    }

    const idParamName = 'id';
    const appsIdResource = v1Resource.addResource(`{${idParamName}}`);

    // Resource for /apps/v1/key/{key}
    const keyResource = v1Resource.addResource('key');
    const keyParamName = 'key';
    const appsKeyResource = keyResource.addResource(`{${keyParamName}}`);

    const nodeModulesLayerDist = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.commonLayerFolderZipFileName,
    );
    const nodeModulesLayer = new lambda.LayerVersion(this, 'NodeModulesLayer', {
      code: nodeModulesLayerDist,
      compatibleRuntimes: [lambda.Runtime.NODEJS_20_X],
      description: 'Node modules for API',
    });

    const createAppCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-create-apps-lambda`],
    );

    const updateAppCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-update-apps-lambda`],
    );

    const getAppByIdCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-get-app-by-id-lambda`],
    );

    const getAppByKeyCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-get-app-by-key-lambda`],
    );

    const getAppsCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-get-apps-lambda`],
    );

    const deleteAppCode = lambda.Code.fromBucket(
      sourceCodeBucket,
      lambdasDeployment.lambdas[`${stackName}-ah-soft-delete-app-lambda`],
    );

    const lambdaEnvironment: { [key: string]: string } = {
      PRISMA_QUERY_ENGINE_LIBRARY: configPrisma.LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY,
      DB_URL: configPrisma.DB_URL,
    };

    buildAppsCreateApi(this, {
      rootResource: v1Resource,
      tag: () => 'ah',
      code: createAppCode,
      nodeModulesLayer: nodeModulesLayer,
      environment: lambdaEnvironment,
    });

    buildAppsUpdateApi(this, {
      rootResource: appsIdResource,
      tag: () => 'ah',
      code: updateAppCode,
      nodeModulesLayer: nodeModulesLayer,
      idParamName: idParamName,
      environment: lambdaEnvironment,
    });

    buildAppsGetByIdApi(this, {
      rootResource: appsIdResource,
      tag: () => 'ah',
      code: getAppByIdCode,
      nodeModulesLayer: nodeModulesLayer,
      idParamName: idParamName,
      environment: lambdaEnvironment,
    });

    buildAppsGetByKeyApi(this, {
      rootResource: appsKeyResource,
      tag: () => 'ah',
      code: getAppByKeyCode,
      nodeModulesLayer: nodeModulesLayer,
      keyParamName: keyParamName,
      environment: lambdaEnvironment,
    });

    buildAppsGetApi(this, {
      rootResource: v1Resource,
      tag: () => 'ah',
      code: getAppsCode,
      nodeModulesLayer: nodeModulesLayer,
      environment: lambdaEnvironment,
    });

    buildAppsDeleteApi(this, {
      rootResource: appsIdResource,
      tag: () => 'ah',
      code: deleteAppCode,
      nodeModulesLayer: nodeModulesLayer,
      idParamName: idParamName,
      environment: lambdaEnvironment,
    });
  }
}