#!/usr/bin/env bash
#+--------------------------------------------------------------------------------+
#|                                                                                |
#|          <AUTHOR> <<EMAIL>>         |
#|                                                                                |
#+--------------------------------------------------------------------------------+

# https://explainshell.com/explain?cmd=set%20-euxo%20pipefail

set -euxo pipefail

echo "Copying Node Modules"

cd nodejs
npm ci --ignore-scripts --only=prod

echo "Finalizing"

exit 0
