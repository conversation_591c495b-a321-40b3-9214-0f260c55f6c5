{"name": "@microsip/common-layer", "version": "0.0.1", "private": true, "description": "Microsip Arquitectura Hibrida Common Lambda Layer", "engines": {"node": ">=20.0.0"}, "repository": {"type": "git", "url": "https://github.com/Microsip/ah-apps-microservice"}, "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "keywords": ["IAC", "AWS", "CDK", "Serverless", "Microservices", "Lambda", "Layer", "Microsip", "Arquitectura Hibrida"], "license": "ISC", "homepage": ".", "scripts": {"prebuild": "rimraf nodejs/node_modules nodejs/package.json nodejs/package-lock.json", "build": "echo 'No build my lord'", "build:nodejs": "scripts/build.sh", "postbuild:nodejs": "scripts/post-build.sh", "test": "echo 'No tests my lord'", "test:ci": "echo 'No tests my lord'", "type-check": "tsc --pretty --noEmit", "format": "prettier --write **/*.ts", "lint": "eslint . --ext ts --ignore-pattern '**/*.d.ts' --fix", "lint:staged": "lint-staged"}, "devDependencies": {"prisma": "^6.10.1"}, "dependencies": {"@prisma/client": "^6.10.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "debug": "^4.4.1", "dotenv": "^16.5.0", "find-up": "^7.0.0", "rimraf": "^6.0.1", "semver": "^7.7.2"}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "yarn run type-check"}}, "lint-staged": {"*.@(ts)": ["pretty-quick --staged", "yarn lint", "yarn format"]}}