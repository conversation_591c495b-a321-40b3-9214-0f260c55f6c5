/**
 * Interfaz base para todas las respuestas de la API
 */
export interface BaseApiResponse {
  success: boolean
}

/**
 * Interfaz para respuestas exitosas
 */
export interface SuccessApiResponse<T = any> extends BaseApiResponse {
  success: true
  data?: T
  message: string
}

/**
 * Interfaz para respuestas de error
 */
export interface ErrorApiResponse extends BaseApiResponse {
  success: false
  error: {
    code: number
    message: string
    status: string
    details: Array<{ field: string; message: string }>
  }
}

/**
 * Tipo unión para todas las respuestas posibles
 */
export type ApiResponse<T = any> = SuccessApiResponse<T> | ErrorApiResponse 