import { APIGatewayProxyResultV2 } from 'aws-lambda'
import {
  SuccessApiResponse,
  ApiResponse
} from './baseResponse'
import { SuccessResponseFactory } from './successResponse'

/**
 * Manager unificado para manejar respuestas exitosas de la API
 * Delega la creación de respuestas al SuccessResponseFactory
 * El manejo de errores se delega completamente al ExceptionHandlerManager
 */
export class ApiResponseManager {
  /**
   * Crea una respuesta exitosa delegando al SuccessResponseFactory
   */
  static success<T>(
    data: T,
    message: string = 'Operación realizada exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.ok(data, message)
  }

  /**
   * Crea una respuesta exitosa sin datos
   */
  static successNoData(
    message: string = 'Operación realizada exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.ok(undefined, message)
  }

  /**
   * Crea una respuesta de listado
   */
  static list<T>(
    data: T[],
    message: string = 'Lista obtenida exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.list(data, message)
  }

  /**
   * Crea una respuesta de actualización
   */
  static updated<T>(
    data: T,
    message: string = 'Recurso actualizado exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.updated(data, message)
  }

  /**
   * Crea una respuesta de eliminación
   */
  static deleted(
    message: string = 'Recurso eliminado exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.deleted(message)
  }

  /**
   * Crea una respuesta de creación
   */
  static created<T>(
    data: T,
    message: string = 'Recurso creado exitosamente'
  ): APIGatewayProxyResultV2 {
    return SuccessResponseFactory.created(data, message)
  }

  /**
   * Valida si una respuesta es exitosa
   */
  static isSuccess<T>(apiResponse: ApiResponse<T>): apiResponse is SuccessApiResponse<T> {
    return apiResponse.success === true
  }

  /**
   * Valida si una respuesta es de error
   */
  static isError(apiResponse: ApiResponse): apiResponse is { success: false; error: any } {
    return apiResponse.success === false
  }
} 