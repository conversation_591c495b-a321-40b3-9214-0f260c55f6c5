import { AppResponse, createAppResponse } from '@microsip/models';
import { AppsRepository } from '@microsip/domain';
import { NotFoundException } from '@microsip/http-layer';

const appsRepository = new AppsRepository();

export async function getAppByKey(key: string): Promise<AppResponse> {
    const app = await appsRepository.findByKey(key);

    if (!app || app.deleted) {
        throw new NotFoundException(
            `No existe un aplicativo con la clave ${key}`,
            [{ field: 'key', message: `No se encontró un aplicativo con la clave: ${key}` }]
        );
    }

    return createAppResponse(app);
}
