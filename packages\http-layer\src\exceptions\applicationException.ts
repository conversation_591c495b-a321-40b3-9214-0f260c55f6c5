import { StatusCodes, ReasonPhrases } from 'http-status-codes'

export class ApplicationException extends Error {
  public details?: Array<{ field: string, message: string }>
  public readonly exceptionName: string

  constructor(
    public message: string,
    details?: Array<{ field: string, message: string }> | null,
    public statusCode: number = StatusCodes.INTERNAL_SERVER_ERROR,
    exceptionName: string = 'ApplicationException'
  ) {
    super(message)
    super.name = 'ApplicationException'
    this.exceptionName = exceptionName
    if (details && details.length > 0) {
      this.details = details
    } else {
      this.details = []
    }
  }

  /**
   * Obtiene el status basado en el statusCode
   */
  get status(): string {
    return this.getStatusFromCode(this.statusCode)
  }

  /**
   * Función auxiliar para mapear statusCode a status usando ReasonPhrases y convertir al formato requerido
   */
  private getStatusFromCode(code: number): string {
    const statusMap: Record<number, string> = {
      [StatusCodes.BAD_REQUEST]: ReasonPhrases.BAD_REQUEST.toUpperCase(),
      [StatusCodes.CONFLICT]: ReasonPhrases.CONFLICT.toUpperCase(),
      [StatusCodes.NOT_FOUND]: ReasonPhrases.NOT_FOUND.toUpperCase(),
      [StatusCodes.UNPROCESSABLE_ENTITY]: ReasonPhrases.UNPROCESSABLE_ENTITY.toUpperCase(),
      [StatusCodes.NOT_ACCEPTABLE]: ReasonPhrases.NOT_ACCEPTABLE.toUpperCase(),
      [StatusCodes.UNAUTHORIZED]: ReasonPhrases.UNAUTHORIZED.toUpperCase(),
      [StatusCodes.FORBIDDEN]: ReasonPhrases.FORBIDDEN.toUpperCase(),
      [StatusCodes.INTERNAL_SERVER_ERROR]: ReasonPhrases.INTERNAL_SERVER_ERROR.toUpperCase(),
    }

    const reasonPhrase = statusMap[code] || 'Error'

    return reasonPhrase.replace(/\s+/g, '_')
  }
}
