{"extends": "../../tsconfig.json", "compilerOptions": {"paths": {"@microsip/util": ["../util/src"]}, "module": "commonjs", "target": "es2019", "lib": ["es2019"], "strict": true, "declaration": true, "skipLibCheck": true, "noUnusedParameters": true, "noUnusedLocals": true, "noImplicitThis": true, "noImplicitReturns": true, "removeComments": true, "noFallthroughCasesInSwitch": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "outDir": ".dist"}, "include": ["src/**/*"], "exclude": ["jest.config.ts"]}