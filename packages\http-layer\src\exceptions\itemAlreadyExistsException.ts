import { StatusCodes } from 'http-status-codes'
import { ApplicationException } from './applicationException'

export class ItemAlreadyExistsException extends ApplicationException {
  constructor(
    message: string = 'El elemento ya existe',
    details?: Array<{ field: string, message: string }>
  ) {
    super(
      message,
      details,
      StatusCodes.CONFLICT,
      'ItemAlreadyExistsException'
    )
  }
}
