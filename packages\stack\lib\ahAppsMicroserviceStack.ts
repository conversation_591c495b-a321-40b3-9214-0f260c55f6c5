import * as cdk from 'aws-cdk-lib';
import * as s3 from 'aws-cdk-lib/aws-s3';
import { Construct } from 'constructs';
import { PackAndPublishLambdasReturnProps } from './model';
import { RestApiStack } from './stacks';
import { PrismaConfig } from './api';

const { STACK_NAME, DB_URL, AWS_REGION, LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY } = process.env;

interface MicrosipAppStackProps extends cdk.StackProps {
  lambdasDeployment: PackAndPublishLambdasReturnProps
}

export class AHAppsMicroserviceStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props?: MicrosipAppStackProps) {
    super(scope, id, props);

    if (!STACK_NAME || !DB_URL || !AWS_REGION || !LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY) {
      throw new Error('STACK_NAME, DB_URL, AWS_REGION and LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY must be set');
    }

    if (!props?.lambdasDeployment) {
      throw new Error('lambdasDeployment is required');
    }

    const restApiId = cdk.Fn.importValue('AHRestApiId');
    console.log(restApiId);

    const rootResourceId = cdk.Fn.importValue('AHRestApiResourceId');
    console.log(rootResourceId);

    const sourceCodeBucket = s3.Bucket.fromBucketName(
      this,
      'LambdasSourceCodeBucket',
      props.lambdasDeployment.sourceCodeBucketName
    );

    const configPrisma: PrismaConfig = {
      DB_URL: DB_URL,
      LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY: LAMBDAS_PRISMA_QUERY_ENGINE_LIBRARY,
    };

    const restApiStack = new RestApiStack(this, 'RestApiStack', {
      restApiId,
      restApiName: "RestApi",
      rootResourceId,
      stackName: id,
      lambdasDeployment: props.lambdasDeployment,
      sourceCodeBucket,
      configPrisma
    });
    
  }
}
